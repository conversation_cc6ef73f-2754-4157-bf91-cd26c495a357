import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:medicare_app/core/constants/app_constants.dart';
import 'package:medicare_app/features/authentication/data/models/user_model.dart';

class DoctorModel extends UserModel {
  final String? medicalLicenseNumber;
  final String? specialization;
  final List<String>? subSpecializations;
  final int? yearsOfExperience;
  final String? education;
  final List<String>? certifications;
  final String? hospitalAffiliation;
  final String? clinicAddress;
  final double? consultationFee;
  final double? rating;
  final int? totalReviews;
  final List<String>? languages;
  final Map<String, dynamic>? availability;
  final bool isVerified;
  final bool isAvailable;
  final String? about;

  const DoctorModel({
    required super.id,
    required super.email,
    required super.firstName,
    required super.lastName,
    super.phoneNumber,
    super.dateOfBirth,
    super.gender,
    super.profileImageUrl,
    super.isActive,
    super.isEmailVerified,
    required super.createdAt,
    required super.updatedAt,
    super.additionalData,
    this.medicalLicenseNumber,
    this.specialization,
    this.subSpecializations,
    this.yearsOfExperience,
    this.education,
    this.certifications,
    this.hospitalAffiliation,
    this.clinicAddress,
    this.consultationFee,
    this.rating,
    this.totalReviews,
    this.languages,
    this.availability,
    this.isVerified = false,
    this.isAvailable = true,
    this.about,
  }) : super(role: AppConstants.doctorRole);

  factory DoctorModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return DoctorModel(
      id: doc.id,
      email: data['email'] ?? '',
      firstName: data['firstName'] ?? '',
      lastName: data['lastName'] ?? '',
      phoneNumber: data['phoneNumber'],
      dateOfBirth: data['dateOfBirth'] != null 
          ? (data['dateOfBirth'] as Timestamp).toDate()
          : null,
      gender: data['gender'],
      profileImageUrl: data['profileImageUrl'],
      isActive: data['isActive'] ?? true,
      isEmailVerified: data['isEmailVerified'] ?? false,
      createdAt: data['createdAt'] != null 
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: data['updatedAt'] != null 
          ? (data['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
      additionalData: data['additionalData'],
      medicalLicenseNumber: data['medicalLicenseNumber'],
      specialization: data['specialization'],
      subSpecializations: data['subSpecializations'] != null 
          ? List<String>.from(data['subSpecializations'])
          : null,
      yearsOfExperience: data['yearsOfExperience'],
      education: data['education'],
      certifications: data['certifications'] != null 
          ? List<String>.from(data['certifications'])
          : null,
      hospitalAffiliation: data['hospitalAffiliation'],
      clinicAddress: data['clinicAddress'],
      consultationFee: data['consultationFee']?.toDouble(),
      rating: data['rating']?.toDouble(),
      totalReviews: data['totalReviews'],
      languages: data['languages'] != null 
          ? List<String>.from(data['languages'])
          : null,
      availability: data['availability'],
      isVerified: data['isVerified'] ?? false,
      isAvailable: data['isAvailable'] ?? true,
      about: data['about'],
    );
  }

  @override
  Map<String, dynamic> toFirestore() {
    final baseData = super.toFirestore();
    baseData.addAll({
      'medicalLicenseNumber': medicalLicenseNumber,
      'specialization': specialization,
      'subSpecializations': subSpecializations,
      'yearsOfExperience': yearsOfExperience,
      'education': education,
      'certifications': certifications,
      'hospitalAffiliation': hospitalAffiliation,
      'clinicAddress': clinicAddress,
      'consultationFee': consultationFee,
      'rating': rating,
      'totalReviews': totalReviews,
      'languages': languages,
      'availability': availability,
      'isVerified': isVerified,
      'isAvailable': isAvailable,
      'about': about,
    });
    return baseData;
  }

  String get experienceText {
    if (yearsOfExperience == null) return 'Experience not specified';
    if (yearsOfExperience == 1) return '1 year experience';
    return '$yearsOfExperience years experience';
  }

  String get ratingText {
    if (rating == null) return 'No rating';
    return '${rating!.toStringAsFixed(1)} ★';
  }

  String get consultationFeeText {
    if (consultationFee == null) return 'Fee not specified';
    return '\$${consultationFee!.toStringAsFixed(2)}';
  }

  bool get hasCompleteProfile {
    return medicalLicenseNumber != null &&
           specialization != null &&
           yearsOfExperience != null &&
           education != null &&
           consultationFee != null;
  }

  @override
  DoctorModel copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    DateTime? dateOfBirth,
    String? gender,
    String? profileImageUrl,
    String? role,
    bool? isActive,
    bool? isEmailVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalData,
  }) {
    return DoctorModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isActive: isActive ?? this.isActive,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalData: additionalData ?? this.additionalData,
      medicalLicenseNumber: medicalLicenseNumber,
      specialization: specialization,
      subSpecializations: subSpecializations,
      yearsOfExperience: yearsOfExperience,
      education: education,
      certifications: certifications,
      hospitalAffiliation: hospitalAffiliation,
      clinicAddress: clinicAddress,
      consultationFee: consultationFee,
      rating: rating,
      totalReviews: totalReviews,
      languages: languages,
      availability: availability,
      isVerified: isVerified,
      isAvailable: isAvailable,
      about: about,
    );
  }

  // Doctor-specific copyWith method
  DoctorModel copyWithDoctorData({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    DateTime? dateOfBirth,
    String? gender,
    String? profileImageUrl,
    bool? isActive,
    bool? isEmailVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalData,
    String? medicalLicenseNumber,
    String? specialization,
    List<String>? subSpecializations,
    int? yearsOfExperience,
    String? education,
    List<String>? certifications,
    String? hospitalAffiliation,
    String? clinicAddress,
    double? consultationFee,
    double? rating,
    int? totalReviews,
    List<String>? languages,
    Map<String, dynamic>? availability,
    bool? isVerified,
    bool? isAvailable,
    String? about,
  }) {
    return DoctorModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isActive: isActive ?? this.isActive,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalData: additionalData ?? this.additionalData,
      medicalLicenseNumber: medicalLicenseNumber ?? this.medicalLicenseNumber,
      specialization: specialization ?? this.specialization,
      subSpecializations: subSpecializations ?? this.subSpecializations,
      yearsOfExperience: yearsOfExperience ?? this.yearsOfExperience,
      education: education ?? this.education,
      certifications: certifications ?? this.certifications,
      hospitalAffiliation: hospitalAffiliation ?? this.hospitalAffiliation,
      clinicAddress: clinicAddress ?? this.clinicAddress,
      consultationFee: consultationFee ?? this.consultationFee,
      rating: rating ?? this.rating,
      totalReviews: totalReviews ?? this.totalReviews,
      languages: languages ?? this.languages,
      availability: availability ?? this.availability,
      isVerified: isVerified ?? this.isVerified,
      isAvailable: isAvailable ?? this.isAvailable,
      about: about ?? this.about,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        medicalLicenseNumber,
        specialization,
        subSpecializations,
        yearsOfExperience,
        education,
        certifications,
        hospitalAffiliation,
        clinicAddress,
        consultationFee,
        rating,
        totalReviews,
        languages,
        availability,
        isVerified,
        isAvailable,
        about,
      ];
}

// Availability model for doctors
class DoctorAvailability {
  final String dayOfWeek;
  final List<TimeSlot> timeSlots;
  final bool isAvailable;

  const DoctorAvailability({
    required this.dayOfWeek,
    required this.timeSlots,
    this.isAvailable = true,
  });

  factory DoctorAvailability.fromJson(Map<String, dynamic> json) {
    return DoctorAvailability(
      dayOfWeek: json['dayOfWeek'],
      timeSlots: (json['timeSlots'] as List)
          .map((slot) => TimeSlot.fromJson(slot))
          .toList(),
      isAvailable: json['isAvailable'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dayOfWeek': dayOfWeek,
      'timeSlots': timeSlots.map((slot) => slot.toJson()).toList(),
      'isAvailable': isAvailable,
    };
  }
}

class TimeSlot {
  final String startTime;
  final String endTime;
  final bool isBooked;

  const TimeSlot({
    required this.startTime,
    required this.endTime,
    this.isBooked = false,
  });

  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      startTime: json['startTime'],
      endTime: json['endTime'],
      isBooked: json['isBooked'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startTime': startTime,
      'endTime': endTime,
      'isBooked': isBooked,
    };
  }
}
