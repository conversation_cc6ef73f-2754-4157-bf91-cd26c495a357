import 'package:flutter/material.dart';
import 'package:medicare_app/core/constants/app_colors.dart';
import 'package:medicare_app/core/services/user_service.dart';
import 'package:medicare_app/core/widgets/card_3d.dart';

class MessagesScreen extends StatefulWidget {
  const MessagesScreen({super.key});

  @override
  State<MessagesScreen> createState() => _MessagesScreenState();
}

class _MessagesScreenState extends State<MessagesScreen> {
  String userRole = 'patient';
  List<Map<String, dynamic>> conversations = [];

  @override
  void initState() {
    super.initState();
    _loadUserRole();
    _loadConversations();
  }

  Future<void> _loadUserRole() async {
    final role = await UserService.instance.getUserRole();
    setState(() {
      userRole = role;
    });
  }

  void _loadConversations() {
    // Simulate loading conversations
    setState(() {
      conversations = _getConversations();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        title: Row(
          children: [
            Icon(Icons.chat_bubble_outline, size: 24),
            const SizedBox(width: 8),
            const Text('الرسائل'),
          ],
        ),
        centerTitle: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _searchMessages,
            tooltip: 'البحث في الرسائل',
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: conversations.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
              itemCount: conversations.length,
              itemBuilder: (context, index) {
                return _buildConversationCard(conversations[index]);
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _startNewConversation,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد رسائل',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            userRole == 'patient'
                ? 'ابدأ محادثة مع طبيبك'
                : 'ابدأ محادثة مع مرضاك',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _startNewConversation,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.textOnPrimary,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('بدء محادثة جديدة'),
          ),
        ],
      ),
    );
  }

  Widget _buildConversationCard(Map<String, dynamic> conversation) {
    final gradient = _getConversationGradient(conversation);

    return Card3D(
      margin: const EdgeInsets.only(bottom: 12),
      gradient: gradient,
      onTap: () => _openConversation(conversation),
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Avatar
          Stack(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  conversation['name'][0],
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
              if (conversation['isOnline'])
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: AppColors.success,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 12),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        conversation['name'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    Text(
                      conversation['time'],
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    if (conversation['lastMessageSent'])
                      Icon(
                        Icons.done_all,
                        size: 16,
                        color: conversation['lastMessageRead']
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.7),
                      ),
                    if (conversation['lastMessageSent'])
                      const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        conversation['lastMessage'],
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                          fontWeight: conversation['unreadCount'] > 0
                              ? FontWeight.w500
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                    if (conversation['unreadCount'] > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          conversation['unreadCount'].toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                if (conversation['role'] != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    conversation['role'],
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.8),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getConversations() {
    if (userRole == 'patient') {
      return [
        {
          'id': '1',
          'name': 'د. سارة أحمد',
          'role': 'أخصائي طب الأطفال',
          'lastMessage': 'تم مراجعة التحاليل، النتائج طبيعية والحمد لله',
          'time': '10:30 ص',
          'unreadCount': 2,
          'isOnline': true,
          'lastMessageSent': false,
          'lastMessageRead': true,
        },
        {
          'id': '2',
          'name': 'د. محمد علي',
          'role': 'أخصائي الطب الباطني',
          'lastMessage': 'موعدك القادم يوم الأحد الساعة 2:00 مساءً',
          'time': 'أمس',
          'unreadCount': 0,
          'isOnline': false,
          'lastMessageSent': true,
          'lastMessageRead': true,
        },
        {
          'id': '3',
          'name': 'د. فاطمة حسن',
          'role': 'أخصائي طب وجراحة العيون',
          'lastMessage': 'تم إرسال الوصفة الطبية إلى الصيدلية',
          'time': 'الأحد',
          'unreadCount': 0,
          'isOnline': true,
          'lastMessageSent': false,
          'lastMessageRead': true,
        },
      ];
    } else {
      return [
        {
          'id': '1',
          'name': 'أحمد محمد السالم',
          'role': 'مريض • 35 سنة',
          'lastMessage': 'شكراً دكتور، تحسنت حالتي كثيراً بعد العلاج',
          'time': '11:15 ص',
          'unreadCount': 1,
          'isOnline': true,
          'lastMessageSent': false,
          'lastMessageRead': true,
        },
        {
          'id': '2',
          'name': 'فاطمة علي أحمد',
          'role': 'مريضة • 28 سنة',
          'lastMessage': 'هل يمكن تأجيل موعد الغد لظرف طارئ؟',
          'time': '9:45 ص',
          'unreadCount': 3,
          'isOnline': false,
          'lastMessageSent': false,
          'lastMessageRead': false,
        },
        {
          'id': '3',
          'name': 'سعد الأحمد محمد',
          'role': 'مريض • 42 سنة',
          'lastMessage': 'تم استلام التقرير الطبي، شكراً لاهتمامكم',
          'time': 'أمس',
          'unreadCount': 0,
          'isOnline': true,
          'lastMessageSent': true,
          'lastMessageRead': true,
        },
      ];
    }
  }

  void _searchMessages() {
    showSearch(
      context: context,
      delegate: MessageSearchDelegate(),
    );
  }

  void _startNewConversation() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('بدء محادثة جديدة قريباً...'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  LinearGradient _getConversationGradient(Map<String, dynamic> conversation) {
    // تحديد اللون حسب حالة المحادثة
    if (conversation['unreadCount'] > 0) {
      return AppColors.blueGradient; // رسائل غير مقروءة
    } else if (conversation['isOnline']) {
      return AppColors.greenGradient; // متصل الآن
    } else {
      return AppColors.tealGradient; // حالة عادية
    }
  }

  void _openConversation(Map<String, dynamic> conversation) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(conversation: conversation),
      ),
    );
  }
}

class MessageSearchDelegate extends SearchDelegate<String> {
  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return Center(
      child: Text('البحث عن: $query'),
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return const Center(
      child: Text('ابحث عن رسالة أو شخص...'),
    );
  }
}

class ChatScreen extends StatefulWidget {
  final Map<String, dynamic> conversation;

  const ChatScreen({super.key, required this.conversation});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final List<Map<String, dynamic>> _messages = [];

  @override
  void initState() {
    super.initState();
    _loadMessages();
  }

  void _loadMessages() {
    // Simulate loading messages
    setState(() {
      _messages.addAll([
        {
          'id': '1',
          'text': 'مرحباً دكتور، كيف حالك؟',
          'isSent': true,
          'time': '10:00 ص',
          'isRead': true,
        },
        {
          'id': '2',
          'text': 'مرحباً، أهلاً وسهلاً. كيف يمكنني مساعدتك؟',
          'isSent': false,
          'time': '10:05 ص',
          'isRead': true,
        },
        {
          'id': '3',
          'text': 'أريد استشارة حول نتائج التحاليل',
          'isSent': true,
          'time': '10:10 ص',
          'isRead': true,
        },
      ]);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        title: Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: AppColors.textOnPrimary,
              child: Text(
                widget.conversation['name'][0],
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.conversation['name'],
                    style: const TextStyle(fontSize: 16),
                  ),
                  if (widget.conversation['isOnline'])
                    const Text(
                      'متصل الآن',
                      style: TextStyle(fontSize: 12),
                    ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.videocam),
            onPressed: _startVideoCall,
          ),
          IconButton(
            icon: const Icon(Icons.call),
            onPressed: _startVoiceCall,
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                return _buildMessageBubble(_messages[index]);
              },
            ),
          ),
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(Map<String, dynamic> message) {
    final isSent = message['isSent'];
    
    return Align(
      alignment: isSent ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.7,
        ),
        decoration: BoxDecoration(
          color: isSent ? AppColors.primary : AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          border: isSent ? null : Border.all(color: AppColors.border),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message['text'],
              style: TextStyle(
                color: isSent ? AppColors.textOnPrimary : AppColors.textPrimary,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  message['time'],
                  style: TextStyle(
                    color: isSent 
                        ? AppColors.textOnPrimary.withValues(alpha: 0.7)
                        : AppColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
                if (isSent) ...[
                  const SizedBox(width: 4),
                  Icon(
                    Icons.done_all,
                    size: 16,
                    color: message['isRead']
                        ? AppColors.textOnPrimary
                        : AppColors.textOnPrimary.withValues(alpha: 0.7),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(top: BorderSide(color: AppColors.border)),
      ),
      child: Row(
        children: [
          IconButton(
            icon: Icon(Icons.attach_file, color: AppColors.primary),
            onPressed: _attachFile,
          ),
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'اكتب رسالة...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: AppColors.background,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              maxLines: null,
            ),
          ),
          const SizedBox(width: 8),
          CircleAvatar(
            backgroundColor: AppColors.primary,
            child: IconButton(
              icon: const Icon(Icons.send, color: Colors.white),
              onPressed: _sendMessage,
            ),
          ),
        ],
      ),
    );
  }

  void _sendMessage() {
    if (_messageController.text.trim().isNotEmpty) {
      setState(() {
        _messages.add({
          'id': DateTime.now().millisecondsSinceEpoch.toString(),
          'text': _messageController.text.trim(),
          'isSent': true,
          'time': '${DateTime.now().hour}:${DateTime.now().minute.toString().padLeft(2, '0')}',
          'isRead': false,
        });
      });
      _messageController.clear();
    }
  }

  void _attachFile() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('إرفاق ملف قريباً...'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _startVideoCall() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('مكالمة فيديو قريباً...'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _startVoiceCall() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('مكالمة صوتية قريباً...'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }
}
