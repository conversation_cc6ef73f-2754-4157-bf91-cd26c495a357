import 'package:flutter/material.dart';
import 'package:medicare_app/core/constants/app_colors.dart';
import 'package:medicare_app/core/constants/app_strings.dart';
import 'package:medicare_app/core/constants/app_routes.dart';
import 'package:medicare_app/core/services/user_service.dart';
import 'package:medicare_app/core/widgets/card_3d.dart';
import 'package:medicare_app/features/appointments/presentation/screens/appointments_screen.dart';
import 'package:medicare_app/features/messages/presentation/screens/messages_screen.dart';
import 'package:medicare_app/features/profile/presentation/screens/profile_screen.dart';
import 'package:medicare_app/features/ai_assistant/presentation/screens/medical_chatbot_screen.dart';
import 'package:medicare_app/features/ai_assistant/presentation/screens/symptom_checker_screen.dart';

class DashboardScreen extends StatefulWidget {
  final String userRole;
  final String userName;

  const DashboardScreen({
    super.key,
    required this.userRole,
    required this.userName,
  });

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        title: Text(
          'Welcome, ${widget.userName}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: _handleNotifications,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuSelection,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(Icons.person_outline),
                    SizedBox(width: 8),
                    Text('Profile'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings_outlined),
                    SizedBox(width: 8),
                    Text('Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout),
                    SizedBox(width: 8),
                    Text('Logout'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return _buildSecondTab();
      case 2:
        return _buildThirdTab();
      case 3:
        return _buildFourthTab();
      default:
        return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      widget.userRole == 'patient' ? Icons.favorite : Icons.medical_services,
                      color: AppColors.textOnPrimary,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${_getGreeting()}، ${widget.userName}',
                            style: TextStyle(
                              color: AppColors.textOnPrimary,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.userRole == 'patient'
                                ? 'كيف تشعر اليوم؟'
                                : 'مستعد لمساعدة مرضاك؟',
                            style: TextStyle(
                              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
                              fontSize: 15,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Quick Actions Section
          Row(
            children: [
              Icon(
                Icons.flash_on,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'الإجراءات السريعة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          _buildQuickActions(),
          const SizedBox(height: 24),

          // Recent Activity Section
          Row(
            children: [
              Icon(
                Icons.timeline,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                widget.userRole == 'patient' ? 'النشاط الأخير' : 'نظرة عامة على اليوم',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 33),

          _buildRecentActivity(),
          const SizedBox(height: 100), // مساحة إضافية للتنقل السفلي
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    if (widget.userRole == 'patient') {
      return SizedBox(
        height: 280, // ارتفاع ثابت للشبكة
        child: GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 24,
          mainAxisSpacing: 3,
          childAspectRatio: 1.1,
          children: [
          ActionCard3D(
            icon: Icons.calendar_today,
            title: 'حجز موعد',
            subtitle: 'احجز مع طبيبك المفضل',
            gradient: AppColors.blueGradient,
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const AppointmentsScreen(),
              ),
            ),
          ),
          ActionCard3D(
            icon: Icons.psychology,
            title: 'فحص الأعراض',
            subtitle: 'تحليل ذكي للأعراض',
            gradient: AppColors.purpleGradient,
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const SymptomCheckerScreen(),
              ),
            ),
          ),
          ActionCard3D(
            icon: Icons.smart_toy,
            title: 'المساعد الطبي',
            subtitle: 'استشارة طبية فورية',
            gradient: AppColors.tealGradient,
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const MedicalChatbotScreen(),
              ),
            ),
          ),
          ActionCard3D(
            icon: Icons.folder_outlined,
            title: 'السجلات الطبية',
            subtitle: 'تاريخك الطبي الكامل',
            gradient: AppColors.orangeGradient,
            onTap: () => _showComingSoon('Medical Records'),
          ),
        ],
        ),
      );
    } else {
      return SizedBox(
        height: 280, // ارتفاع ثابت للشبكة
        child: GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.1,
          children: [
          ActionCard3D(
            icon: Icons.people,
            title: 'قائمة المرضى',
            subtitle: 'إدارة ومتابعة المرضى',
            gradient: AppColors.greenGradient,
            onTap: () => _showComingSoon('My Patients'),
          ),
          ActionCard3D(
            icon: Icons.schedule,
            title: 'جدول المواعيد',
            subtitle: 'تنظيم وإدارة المواعيد',
            gradient: AppColors.indigoGradient,
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const AppointmentsScreen(),
              ),
            ),
          ),
          ActionCard3D(
            icon: Icons.video_call,
            title: 'الاستشارة المرئية',
            subtitle: 'مكالمات فيديو طبية',
            gradient: AppColors.redGradient,
            onTap: () => _showComingSoon('Video Consultation'),
          ),
          ActionCard3D(
            icon: Icons.analytics,
            title: 'التقارير والإحصائيات',
            subtitle: 'تحليل البيانات الطبية',
            gradient: AppColors.cyanGradient,
            onTap: () => _showComingSoon('Analytics'),
          ),
        ],
        ),
      );
    }
  }



  Widget _buildRecentActivity() {
    return Column(
      children: [
        InfoCard3D(
          icon: Icons.check_circle,
          title: widget.userRole == 'patient'
              ? 'آخر موعد مكتمل'
              : 'آخر استشارة مكتملة',
          value: widget.userRole == 'patient'
              ? 'د. سارة أحمد • منذ ساعتين'
              : 'أحمد محمد • منذ ساعة واحدة',
          gradient: AppColors.greenGradient,
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AppointmentsScreen(),
            ),
          ),
        ),
        const SizedBox(height: 12),
        InfoCard3D(
          icon: Icons.schedule,
          title: widget.userRole == 'patient'
              ? 'الموعد القادم'
              : 'الموعد التالي',
          value: widget.userRole == 'patient'
              ? 'غداً • الساعة 10:00 صباحاً'
              : 'خلال 30 دقيقة • د. محمد علي',
          gradient: AppColors.orangeGradient,
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AppointmentsScreen(),
            ),
          ),
        ),
        const SizedBox(height: 12),
        InfoCard3D(
          icon: widget.userRole == 'patient' ? Icons.medication : Icons.analytics,
          title: widget.userRole == 'patient'
              ? 'تذكير الأدوية'
              : 'إحصائيات اليوم',
          value: widget.userRole == 'patient'
              ? 'فيتامين د • الساعة 8:00 مساءً'
              : '12 مريض • 8 استشارات مكتملة',
          gradient: AppColors.purpleGradient,
          onTap: () => _showComingSoon(widget.userRole == 'patient'
              ? 'Medication Reminders'
              : 'Analytics'),
        ),
      ],
    );
  }



  Widget _buildSecondTab() {
    return const AppointmentsScreen();
  }

  Widget _buildThirdTab() {
    return const MessagesScreen();
  }

  Widget _buildFourthTab() {
    return const ProfileScreen();
  }

  Widget _buildBottomNavigationBar() {
    if (widget.userRole == 'patient') {
      return BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today),
            label: 'Appointments',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.chat),
            label: 'Messages',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      );
    } else {
      return BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'Patients',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.chat),
            label: 'Messages',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      );
    }
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'Morning';
    if (hour < 17) return 'Afternoon';
    return 'Evening';
  }

  void _handleNotifications() {
    _showComingSoon('Notifications');
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'profile':
        _showComingSoon('Profile');
        break;
      case 'settings':
        _showComingSoon('Settings');
        break;
      case 'logout':
        _handleLogout();
        break;
    }
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              await UserService.instance.logout();
              if (mounted) {
                navigator.pop(); // Close dialog
                navigator.pushNamedAndRemoveUntil(
                  AppRoutes.login,
                  (route) => false,
                );
              }
            },
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature feature coming soon!'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
