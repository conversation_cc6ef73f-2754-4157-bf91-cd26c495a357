import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

abstract class NetworkInfo {
  Future<bool> get isConnected;
  Future<List<ConnectivityResult>> get connectionType;
  Stream<List<ConnectivityResult>> get onConnectivityChanged;
}

class NetworkInfoImpl implements NetworkInfo {
  final Connectivity connectivity;
  final InternetConnectionChecker connectionChecker;

  NetworkInfoImpl({
    required this.connectivity,
    required this.connectionChecker,
  });

  @override
  Future<bool> get isConnected async {
    try {
      final connectivityResults = await connectivity.checkConnectivity();

      // Check if device is connected to a network
      if (connectivityResults.contains(ConnectivityResult.none)) {
        return false;
      }

      // Check if there's actual internet connectivity
      return await connectionChecker.hasConnection;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<ConnectivityResult>> get connectionType async {
    try {
      return await connectivity.checkConnectivity();
    } catch (e) {
      return [ConnectivityResult.none];
    }
  }

  @override
  Stream<List<ConnectivityResult>> get onConnectivityChanged {
    return connectivity.onConnectivityChanged;
  }
}

class NetworkConstants {
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds
  
  static const String noInternetMessage = 'No internet connection';
  static const String timeoutMessage = 'Connection timeout';
  static const String serverErrorMessage = 'Server error occurred';
  static const String unknownErrorMessage = 'Unknown error occurred';
}

enum NetworkStatus {
  connected,
  disconnected,
  connecting,
}

class NetworkStatusNotifier {
  static final NetworkStatusNotifier _instance = NetworkStatusNotifier._internal();
  factory NetworkStatusNotifier() => _instance;
  NetworkStatusNotifier._internal();

  final Connectivity _connectivity = Connectivity();
  final InternetConnectionChecker _connectionChecker = InternetConnectionChecker.createInstance();
  
  NetworkStatus _status = NetworkStatus.disconnected;
  NetworkStatus get status => _status;
  
  final List<Function(NetworkStatus)> _listeners = [];
  
  void addListener(Function(NetworkStatus) listener) {
    _listeners.add(listener);
  }
  
  void removeListener(Function(NetworkStatus) listener) {
    _listeners.remove(listener);
  }
  
  void _notifyListeners(NetworkStatus status) {
    _status = status;
    for (final listener in _listeners) {
      listener(status);
    }
  }
  
  void startMonitoring() {
    _connectivity.onConnectivityChanged.listen((List<ConnectivityResult> results) async {
      if (results.contains(ConnectivityResult.none)) {
        _notifyListeners(NetworkStatus.disconnected);
      } else {
        _notifyListeners(NetworkStatus.connecting);
        final hasConnection = await _connectionChecker.hasConnection;
        _notifyListeners(hasConnection ? NetworkStatus.connected : NetworkStatus.disconnected);
      }
    });
  }
  
  void stopMonitoring() {
    _listeners.clear();
  }
  
  Future<bool> checkConnection() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      if (connectivityResults.contains(ConnectivityResult.none)) {
        return false;
      }
      return await _connectionChecker.hasConnection;
    } catch (e) {
      return false;
    }
  }
}
