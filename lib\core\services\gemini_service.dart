import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:medicare_app/core/config/api_config.dart';

class GeminiService {
  static String get _apiKey => ApiConfig.geminiApiKey;
  static GenerativeModel? _model;
  
  static GenerativeModel get model {
    _model ??= GenerativeModel(
      model: 'gemini-1.5-flash',
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
      ),
      safetySettings: [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.medium),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.medium),
      ],
    );
    return _model!;
  }

  // Medical Chatbot Service
  static Future<String> getMedicalAdvice(String question) async {
    try {
      final prompt = '''
أنت طبيب ذكي ومساعد طبي متخصص. يرجى الإجابة على السؤال التالي بطريقة مهنية ومفيدة.
مهم: 
- قدم معلومات طبية دقيقة وموثوقة
- انصح دائماً بمراجعة الطبيب للحالات الخطيرة
- استخدم اللغة العربية في الإجابة
- كن واضحاً ومفهوماً
- لا تقدم تشخيصاً نهائياً، بل معلومات عامة

السؤال: $question

الإجابة:
''';

      final content = [Content.text(prompt)];
      final response = await model.generateContent(content);
      
      return response.text ?? 'عذراً، لم أتمكن من الحصول على إجابة. يرجى المحاولة مرة أخرى.';
    } catch (e) {
      return 'عذراً، حدث خطأ في الاتصال. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.';
    }
  }

  // Symptom Analysis Service
  static Future<Map<String, dynamic>> analyzeSymptoms({
    required List<String> symptoms,
    required String age,
    required String gender,
    String? medicalHistory,
    String? currentMedications,
  }) async {
    try {
      final prompt = '''
أنت طبيب متخصص في تحليل الأعراض. قم بتحليل الأعراض التالية وقدم تقييماً طبياً شاملاً.

معلومات المريض:
- العمر: $age سنة
- الجنس: $gender
- الأعراض: ${symptoms.join(', ')}
${medicalHistory != null ? '- التاريخ المرضي: $medicalHistory' : ''}
${currentMedications != null ? '- الأدوية الحالية: $currentMedications' : ''}

يرجى تقديم التحليل في التنسيق التالي:

## التشخيص المحتمل:
[قائمة بالتشخيصات المحتملة مرتبة حسب الاحتمالية]

## مستوى الخطورة:
[منخفض/متوسط/عالي/طارئ]

## التوصيات:
[توصيات للعلاج والرعاية]

## الأدوية المقترحة:
[قائمة بالأدوية المناسبة مع الجرعات]

## نصائح عامة:
[نصائح للرعاية الذاتية والوقاية]

## متى يجب مراجعة الطبيب:
[علامات التحذير التي تستدعي المراجعة الفورية]

مهم: هذا التحليل للإرشاد فقط وليس بديلاً عن الاستشارة الطبية المباشرة.
''';

      final content = [Content.text(prompt)];
      final response = await model.generateContent(content);
      
      final analysisText = response.text ?? 'لم أتمكن من تحليل الأعراض';
      
      // Parse the response into structured data
      return {
        'fullAnalysis': analysisText,
        'severity': _extractSeverity(analysisText),
        'possibleConditions': _extractConditions(analysisText),
        'recommendations': _extractRecommendations(analysisText),
        'medications': _extractMedications(analysisText),
        'whenToSeeDoctor': _extractWhenToSeeDoctor(analysisText),
      };
    } catch (e) {
      return {
        'error': 'حدث خطأ في تحليل الأعراض. يرجى المحاولة مرة أخرى.',
        'fullAnalysis': 'عذراً، لم أتمكن من تحليل الأعراض في الوقت الحالي.',
        'severity': 'غير محدد',
        'possibleConditions': [],
        'recommendations': [],
        'medications': [],
        'whenToSeeDoctor': 'يُنصح بمراجعة الطبيب للحصول على تشخيص دقيق.',
      };
    }
  }

  // Health Tips Service
  static Future<String> getHealthTips(String category) async {
    try {
      final prompt = '''
أنت خبير في الصحة العامة. قدم نصائح صحية مفيدة وعملية حول: $category

يرجى تقديم:
- 5-7 نصائح عملية ومفيدة
- معلومات علمية موثوقة
- نصائح يمكن تطبيقها في الحياة اليومية
- استخدم اللغة العربية
- اجعل النصائح واضحة ومفهومة

النصائح:
''';

      final content = [Content.text(prompt)];
      final response = await model.generateContent(content);
      
      return response.text ?? 'عذراً، لم أتمكن من الحصول على نصائح صحية في الوقت الحالي.';
    } catch (e) {
      return 'عذراً، حدث خطأ في الحصول على النصائح الصحية.';
    }
  }

  // Drug Information Service
  static Future<String> getDrugInformation(String drugName) async {
    try {
      final prompt = '''
أنت صيدلي متخصص. قدم معلومات شاملة عن الدواء التالي: $drugName

يرجى تقديم المعلومات التالية:
- الاسم العلمي والتجاري
- دواعي الاستعمال
- الجرعة المعتادة
- طريقة الاستخدام
- الآثار الجانبية المحتملة
- التحذيرات والاحتياطات
- التفاعلات الدوائية المهمة
- معلومات التخزين

استخدم اللغة العربية وكن دقيقاً في المعلومات.
انصح دائماً بمراجعة الطبيب أو الصيدلي قبل الاستخدام.

معلومات الدواء:
''';

      final content = [Content.text(prompt)];
      final response = await model.generateContent(content);
      
      return response.text ?? 'عذراً، لم أتمكن من الحصول على معلومات الدواء.';
    } catch (e) {
      return 'عذراً، حدث خطأ في الحصول على معلومات الدواء.';
    }
  }

  // Helper methods to extract structured data from AI response
  static String _extractSeverity(String text) {
    final severityRegex = RegExp(r'مستوى الخطورة:\s*\n?\s*\[?([^\]\n]+)\]?', caseSensitive: false);
    final match = severityRegex.firstMatch(text);
    return match?.group(1)?.trim() ?? 'غير محدد';
  }

  static List<String> _extractConditions(String text) {
    final conditionsRegex = RegExp(r'التشخيص المحتمل:\s*\n(.*?)(?=\n##|\n\n|$)', caseSensitive: false, dotAll: true);
    final match = conditionsRegex.firstMatch(text);
    if (match != null) {
      return match.group(1)!
          .split('\n')
          .where((line) => line.trim().isNotEmpty && !line.startsWith('#'))
          .map((line) => line.replaceAll(RegExp(r'^[-*•]\s*'), '').trim())
          .where((line) => line.isNotEmpty)
          .toList();
    }
    return [];
  }

  static List<String> _extractRecommendations(String text) {
    final recommendationsRegex = RegExp(r'التوصيات:\s*\n(.*?)(?=\n##|\n\n|$)', caseSensitive: false, dotAll: true);
    final match = recommendationsRegex.firstMatch(text);
    if (match != null) {
      return match.group(1)!
          .split('\n')
          .where((line) => line.trim().isNotEmpty && !line.startsWith('#'))
          .map((line) => line.replaceAll(RegExp(r'^[-*•]\s*'), '').trim())
          .where((line) => line.isNotEmpty)
          .toList();
    }
    return [];
  }

  static List<String> _extractMedications(String text) {
    final medicationsRegex = RegExp(r'الأدوية المقترحة:\s*\n(.*?)(?=\n##|\n\n|$)', caseSensitive: false, dotAll: true);
    final match = medicationsRegex.firstMatch(text);
    if (match != null) {
      return match.group(1)!
          .split('\n')
          .where((line) => line.trim().isNotEmpty && !line.startsWith('#'))
          .map((line) => line.replaceAll(RegExp(r'^[-*•]\s*'), '').trim())
          .where((line) => line.isNotEmpty)
          .toList();
    }
    return [];
  }

  static String _extractWhenToSeeDoctor(String text) {
    final doctorRegex = RegExp(r'متى يجب مراجعة الطبيب:\s*\n(.*?)(?=\n##|\n\n|$)', caseSensitive: false, dotAll: true);
    final match = doctorRegex.firstMatch(text);
    return match?.group(1)?.trim() ?? 'يُنصح بمراجعة الطبيب للحصول على تشخيص دقيق.';
  }

  // Check if API key is configured
  static bool get isConfigured => ApiConfig.isGeminiConfigured;
}
