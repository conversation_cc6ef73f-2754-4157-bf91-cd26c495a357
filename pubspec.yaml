name: medicare_app
description: "MediCare - Comprehensive Medical Application connecting patients with doctors"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # Core UI and Icons
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296

  # Firebase Services
  firebase_core: ^3.14.0
  firebase_auth: ^5.3.3
  cloud_firestore: ^5.5.0
  firebase_storage: ^12.3.8
  firebase_messaging: ^15.1.6
  firebase_analytics: ^11.3.6
  firebase_crashlytics: ^4.1.6

  # State Management (BLoC Pattern)
  flutter_bloc: ^8.1.6
  bloc: ^8.1.4
  equatable: ^2.0.5

  # Navigation and Routing
  go_router: ^14.6.2

  # UI and Theming
  google_fonts: ^6.2.1
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  lottie: ^3.1.2

  # HTTP and Networking
  dio: ^5.7.0
  retrofit: ^4.4.1
  json_annotation: ^4.9.0

  # Local Storage
  shared_preferences: ^2.3.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Date and Time
  intl: ^0.19.0
  table_calendar: ^3.1.2

  # Image and File Handling
  image_picker: ^1.1.2
  image_cropper: ^8.0.2
  file_picker: ^8.1.4
  path_provider: ^2.1.4

  # Permissions and Device Features
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2

  # QR Code and Scanning
  mobile_scanner: ^5.2.3
  qr_flutter: ^4.1.0

  # Notifications
  flutter_local_notifications: ^17.2.3

  # AI and ML
  google_generative_ai: ^0.4.6
  flutter_markdown: ^0.7.3
  http: ^1.2.2

  # Video Calling (Agora)
  agora_rtc_engine: ^6.3.2

  # Payment Processing (Stripe)
  flutter_stripe: ^11.1.0

  # OCR and Document Processing
  google_mlkit_text_recognition: ^0.13.1

  # Utilities
  uuid: ^4.5.1
  url_launcher: ^6.3.1
  connectivity_plus: ^6.0.5
  internet_connection_checker: ^3.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  retrofit_generator: ^9.1.2
  hive_generator: ^2.0.1

  # Testing
  bloc_test: ^9.1.7
  mocktail: ^1.0.4
  integration_test:
    sdk: flutter

  # Linting and Code Quality
  flutter_lints: ^5.0.0
  very_good_analysis: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/logos/

  # Fonts will be added later when needed
  # fonts:
  #   - family: Cairo
  #     fonts:
  #       - asset: assets/fonts/Cairo-Regular.ttf
