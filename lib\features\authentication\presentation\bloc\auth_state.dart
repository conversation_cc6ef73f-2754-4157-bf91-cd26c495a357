import 'package:medicare_app/core/base/base_state.dart';
import 'package:medicare_app/core/errors/failures.dart';
import 'package:medicare_app/features/authentication/data/models/user_model.dart';

abstract class AuthState extends BaseState {
  const AuthState();
}

class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  const AuthLoading();
}

class AuthAuthenticated extends AuthState {
  final UserModel user;

  const AuthAuthenticated({
    required this.user,
  });

  @override
  List<Object?> get props => [user];
}

class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

class AuthError extends AuthState {
  final Failure failure;

  const AuthError({
    required this.failure,
  });

  @override
  List<Object?> get props => [failure];
}

class AuthEmailNotVerified extends AuthState {
  final UserModel user;

  const AuthEmailNotVerified({
    required this.user,
  });

  @override
  List<Object?> get props => [user];
}

class AuthEmailVerificationSent extends AuthState {
  final UserModel user;

  const AuthEmailVerificationSent({
    required this.user,
  });

  @override
  List<Object?> get props => [user];
}

class AuthPasswordResetSent extends AuthState {
  const AuthPasswordResetSent();
}

class AuthPasswordChanged extends AuthState {
  final UserModel user;

  const AuthPasswordChanged({
    required this.user,
  });

  @override
  List<Object?> get props => [user];
}

class AuthProfileUpdated extends AuthState {
  final UserModel user;

  const AuthProfileUpdated({
    required this.user,
  });

  @override
  List<Object?> get props => [user];
}

class AuthAccountDeleted extends AuthState {
  const AuthAccountDeleted();
}

// Phone verification states
class AuthPhoneVerificationSent extends AuthState {
  final String verificationId;
  final String phoneNumber;

  const AuthPhoneVerificationSent({
    required this.verificationId,
    required this.phoneNumber,
  });

  @override
  List<Object?> get props => [verificationId, phoneNumber];
}

class AuthPhoneVerificationCompleted extends AuthState {
  final UserModel user;

  const AuthPhoneVerificationCompleted({
    required this.user,
  });

  @override
  List<Object?> get props => [user];
}

class AuthPhoneVerificationFailed extends AuthState {
  final String error;

  const AuthPhoneVerificationFailed({
    required this.error,
  });

  @override
  List<Object?> get props => [error];
}

// Biometric authentication states
class AuthBiometricEnabled extends AuthState {
  const AuthBiometricEnabled();
}

class AuthBiometricDisabled extends AuthState {
  const AuthBiometricDisabled();
}

class AuthBiometricNotAvailable extends AuthState {
  const AuthBiometricNotAvailable();
}

class AuthBiometricAuthenticationSucceeded extends AuthState {
  final UserModel user;

  const AuthBiometricAuthenticationSucceeded({
    required this.user,
  });

  @override
  List<Object?> get props => [user];
}

class AuthBiometricAuthenticationFailed extends AuthState {
  final String error;

  const AuthBiometricAuthenticationFailed({
    required this.error,
  });

  @override
  List<Object?> get props => [error];
}

// Two-factor authentication states
class Auth2FAEnabled extends AuthState {
  final String qrCode;
  final List<String> backupCodes;

  const Auth2FAEnabled({
    required this.qrCode,
    required this.backupCodes,
  });

  @override
  List<Object?> get props => [qrCode, backupCodes];
}

class Auth2FADisabled extends AuthState {
  const Auth2FADisabled();
}

class Auth2FARequired extends AuthState {
  final String tempToken;

  const Auth2FARequired({
    required this.tempToken,
  });

  @override
  List<Object?> get props => [tempToken];
}

class Auth2FAVerified extends AuthState {
  final UserModel user;

  const Auth2FAVerified({
    required this.user,
  });

  @override
  List<Object?> get props => [user];
}

// Social authentication states
class AuthSocialSignInLoading extends AuthState {
  final String provider;

  const AuthSocialSignInLoading({
    required this.provider,
  });

  @override
  List<Object?> get props => [provider];
}

class AuthSocialSignInSuccess extends AuthState {
  final UserModel user;
  final String provider;

  const AuthSocialSignInSuccess({
    required this.user,
    required this.provider,
  });

  @override
  List<Object?> get props => [user, provider];
}

class AuthSocialSignInFailed extends AuthState {
  final String provider;
  final String error;

  const AuthSocialSignInFailed({
    required this.provider,
    required this.error,
  });

  @override
  List<Object?> get props => [provider, error];
}

// Account linking states
class AuthAccountLinked extends AuthState {
  final String provider;
  final UserModel user;

  const AuthAccountLinked({
    required this.provider,
    required this.user,
  });

  @override
  List<Object?> get props => [provider, user];
}

class AuthAccountUnlinked extends AuthState {
  final String provider;
  final UserModel user;

  const AuthAccountUnlinked({
    required this.provider,
    required this.user,
  });

  @override
  List<Object?> get props => [provider, user];
}

class AuthAccountLinkingFailed extends AuthState {
  final String provider;
  final String error;

  const AuthAccountLinkingFailed({
    required this.provider,
    required this.error,
  });

  @override
  List<Object?> get props => [provider, error];
}

// Session management states
class AuthSessionExtended extends AuthState {
  final DateTime expiresAt;

  const AuthSessionExtended({
    required this.expiresAt,
  });

  @override
  List<Object?> get props => [expiresAt];
}

class AuthSessionExpired extends AuthState {
  const AuthSessionExpired();
}

class AuthSessionInvalidated extends AuthState {
  const AuthSessionInvalidated();
}

// Device management states
class AuthDeviceRegistered extends AuthState {
  final String deviceId;

  const AuthDeviceRegistered({
    required this.deviceId,
  });

  @override
  List<Object?> get props => [deviceId];
}

class AuthDeviceUnregistered extends AuthState {
  final String deviceId;

  const AuthDeviceUnregistered({
    required this.deviceId,
  });

  @override
  List<Object?> get props => [deviceId];
}

class AuthDevicesLoaded extends AuthState {
  final List<Map<String, dynamic>> devices;

  const AuthDevicesLoaded({
    required this.devices,
  });

  @override
  List<Object?> get props => [devices];
}

// Security states
class AuthSecuritySettingsUpdated extends AuthState {
  const AuthSecuritySettingsUpdated();
}

class AuthSecurityIncidentReported extends AuthState {
  final String incidentId;

  const AuthSecurityIncidentReported({
    required this.incidentId,
  });

  @override
  List<Object?> get props => [incidentId];
}

// Privacy states
class AuthPrivacySettingsUpdated extends AuthState {
  const AuthPrivacySettingsUpdated();
}

class AuthDataExported extends AuthState {
  final String downloadUrl;

  const AuthDataExported({
    required this.downloadUrl,
  });

  @override
  List<Object?> get props => [downloadUrl];
}

class AuthDataDeleted extends AuthState {
  final List<String> deletedDataTypes;

  const AuthDataDeleted({
    required this.deletedDataTypes,
  });

  @override
  List<Object?> get props => [deletedDataTypes];
}
