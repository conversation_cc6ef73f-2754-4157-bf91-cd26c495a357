import 'package:flutter/material.dart';

/// مكون آمن للتمرير يحل مشكلة الـ overflow
class SafeScrollView extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final Axis scrollDirection;
  final ScrollController? controller;
  final bool addBottomPadding;
  final double bottomPadding;

  const SafeScrollView({
    super.key,
    required this.child,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
    this.scrollDirection = Axis.vertical,
    this.controller,
    this.addBottomPadding = true,
    this.bottomPadding = 100.0,
  });

  @override
  Widget build(BuildContext context) {
    EdgeInsetsGeometry finalPadding = padding ?? const EdgeInsets.all(16);
    
    // إضافة مساحة إضافية في الأسفل إذا كان مطلوباً
    if (addBottomPadding && finalPadding is EdgeInsets) {
      finalPadding = finalPadding.copyWith(
        bottom: finalPadding.bottom + bottomPadding,
      );
    } else if (addBottomPadding) {
      // في حالة EdgeInsetsGeometry أخرى
      finalPadding = EdgeInsets.fromLTRB(
        16,
        16,
        16,
        16 + bottomPadding,
      );
    }

    return SingleChildScrollView(
      controller: controller,
      padding: finalPadding,
      physics: physics,
      scrollDirection: scrollDirection,
      child: child,
    );
  }
}

/// مكون آمن للقوائم يحل مشكلة الـ overflow
class SafeListView extends StatelessWidget {
  final int itemCount;
  final Widget Function(BuildContext, int) itemBuilder;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final Axis scrollDirection;
  final ScrollController? controller;
  final bool addBottomPadding;
  final double bottomPadding;
  final Widget? separator;

  const SafeListView.builder({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
    this.scrollDirection = Axis.vertical,
    this.controller,
    this.addBottomPadding = true,
    this.bottomPadding = 100.0,
    this.separator,
  });

  @override
  Widget build(BuildContext context) {
    EdgeInsetsGeometry finalPadding = padding ?? const EdgeInsets.all(16);
    
    // إضافة مساحة إضافية في الأسفل إذا كان مطلوباً
    if (addBottomPadding && finalPadding is EdgeInsets) {
      finalPadding = finalPadding.copyWith(
        bottom: finalPadding.bottom + bottomPadding,
      );
    } else if (addBottomPadding) {
      // في حالة EdgeInsetsGeometry أخرى
      finalPadding = EdgeInsets.fromLTRB(
        16,
        16,
        16,
        16 + bottomPadding,
      );
    }

    if (separator != null) {
      return ListView.separated(
        controller: controller,
        padding: finalPadding,
        physics: physics,
        shrinkWrap: shrinkWrap,
        scrollDirection: scrollDirection,
        itemCount: itemCount,
        itemBuilder: itemBuilder,
        separatorBuilder: (context, index) => separator!,
      );
    }

    return ListView.builder(
      controller: controller,
      padding: finalPadding,
      physics: physics,
      shrinkWrap: shrinkWrap,
      scrollDirection: scrollDirection,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
    );
  }
}

/// مكون آمن للشبكات يحل مشكلة الـ overflow
class SafeGridView extends StatelessWidget {
  final int itemCount;
  final Widget Function(BuildContext, int) itemBuilder;
  final SliverGridDelegate gridDelegate;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final Axis scrollDirection;
  final ScrollController? controller;
  final bool addBottomPadding;
  final double bottomPadding;

  const SafeGridView.builder({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    required this.gridDelegate,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
    this.scrollDirection = Axis.vertical,
    this.controller,
    this.addBottomPadding = true,
    this.bottomPadding = 100.0,
  });

  @override
  Widget build(BuildContext context) {
    EdgeInsetsGeometry finalPadding = padding ?? const EdgeInsets.all(16);
    
    // إضافة مساحة إضافية في الأسفل إذا كان مطلوباً
    if (addBottomPadding && finalPadding is EdgeInsets) {
      finalPadding = finalPadding.copyWith(
        bottom: finalPadding.bottom + bottomPadding,
      );
    } else if (addBottomPadding) {
      // في حالة EdgeInsetsGeometry أخرى
      finalPadding = EdgeInsets.fromLTRB(
        16,
        16,
        16,
        16 + bottomPadding,
      );
    }

    return GridView.builder(
      controller: controller,
      padding: finalPadding,
      physics: physics,
      shrinkWrap: shrinkWrap,
      scrollDirection: scrollDirection,
      gridDelegate: gridDelegate,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
    );
  }
}

/// مكون للصفحات مع تنقل سفلي آمن
class SafePageView extends StatelessWidget {
  final Widget child;
  final bool hasBottomNavigation;
  final double bottomNavigationHeight;
  final EdgeInsetsGeometry? padding;

  const SafePageView({
    super.key,
    required this.child,
    this.hasBottomNavigation = true,
    this.bottomNavigationHeight = 80.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (!hasBottomNavigation) {
      return child;
    }

    return Padding(
      padding: EdgeInsets.only(
        bottom: bottomNavigationHeight,
      ),
      child: child,
    );
  }
}

/// مساعد للحصول على ارتفاع التنقل السفلي
class NavigationHelper {
  static const double defaultBottomNavHeight = 80.0;
  static const double defaultTabBarHeight = 50.0;
  static const double defaultAppBarHeight = 56.0;
  
  /// حساب المساحة المطلوبة للتنقل السفلي
  static double getBottomPadding({
    bool hasBottomNavigation = true,
    bool hasTabBar = false,
    double customHeight = 0,
  }) {
    double padding = 0;
    
    if (hasBottomNavigation) {
      padding += defaultBottomNavHeight;
    }
    
    if (hasTabBar) {
      padding += defaultTabBarHeight;
    }
    
    if (customHeight > 0) {
      padding += customHeight;
    }
    
    // إضافة مساحة أمان إضافية
    padding += 20;
    
    return padding;
  }
  
  /// الحصول على MediaQuery بشكل آمن
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }
  
  /// حساب الارتفاع المتاح للمحتوى
  static double getAvailableHeight(BuildContext context, {
    bool hasAppBar = true,
    bool hasBottomNavigation = true,
    bool hasTabBar = false,
  }) {
    final mediaQuery = MediaQuery.of(context);
    double availableHeight = mediaQuery.size.height;
    
    // طرح مساحة الـ SafeArea
    availableHeight -= mediaQuery.padding.top;
    availableHeight -= mediaQuery.padding.bottom;
    
    // طرح مساحة AppBar
    if (hasAppBar) {
      availableHeight -= defaultAppBarHeight;
    }
    
    // طرح مساحة التنقل السفلي
    if (hasBottomNavigation) {
      availableHeight -= defaultBottomNavHeight;
    }
    
    // طرح مساحة TabBar
    if (hasTabBar) {
      availableHeight -= defaultTabBarHeight;
    }
    
    return availableHeight;
  }
}

/// Extension لتسهيل استخدام SafeScrollView
extension WidgetSafeScrollExtension on Widget {
  /// تحويل أي Widget إلى SafeScrollView
  Widget toSafeScroll({
    EdgeInsetsGeometry? padding,
    ScrollPhysics? physics,
    bool addBottomPadding = true,
    double bottomPadding = 100.0,
  }) {
    return SafeScrollView(
      padding: padding,
      physics: physics,
      addBottomPadding: addBottomPadding,
      bottomPadding: bottomPadding,
      child: this,
    );
  }
  
  /// إضافة مساحة آمنة في الأسفل
  Widget withBottomPadding([double padding = 100.0]) {
    return Padding(
      padding: EdgeInsets.only(bottom: padding),
      child: this,
    );
  }
}
