import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medicare_app/core/base/base_event.dart';
import 'package:medicare_app/core/base/base_state.dart';
import 'package:medicare_app/core/errors/failures.dart';
import 'package:medicare_app/core/network/network_info.dart';

abstract class BaseBloc<Event extends BaseEvent, State extends BaseState>
    extends Bloc<Event, State> {

  final NetworkInfo? networkInfo;

  BaseBloc(super.initialState, {this.networkInfo});
  
  // Utility methods
  Future<void> handleWithNetworkCheck(
    Future<void> Function() operation,
    Emitter<State> emit,
  ) async {
    if (networkInfo != null) {
      final isConnected = await networkInfo!.isConnected;
      if (!isConnected) {
        emit(const ErrorState(NetworkFailure(message: 'No internet connection')) as State);
        return;
      }
    }
    
    try {
      await operation();
    } catch (e) {
      emit(ErrorState(_mapExceptionToFailure(e)) as State);
    }
  }
  
  Future<T> safeCall<T>(Future<T> Function() operation) async {
    try {
      return await operation();
    } catch (e) {
      throw _mapExceptionToFailure(e);
    }
  }
  
  Failure _mapExceptionToFailure(dynamic exception) {
    if (exception is Failure) {
      return exception;
    }
    
    // Map specific exceptions to failures
    final message = exception.toString();
    
    if (message.contains('network') || message.contains('connection')) {
      return NetworkFailure(message: message);
    } else if (message.contains('server') || message.contains('http')) {
      return ServerFailure(message: message);
    } else if (message.contains('auth') || message.contains('unauthorized')) {
      return AuthenticationFailure(message: message);
    } else if (message.contains('permission')) {
      return AuthorizationFailure(message: message);
    } else if (message.contains('validation')) {
      return ValidationFailure(message: message);
    } else {
      return UnknownFailure(message: message);
    }
  }
  
  // Helper method to emit loading state
  void emitLoading(Emitter<State> emit) {
    emit(const LoadingState() as State);
  }
  
  // Helper method to emit error state
  void emitError(Emitter<State> emit, Failure failure) {
    emit(ErrorState(failure) as State);
  }
  
  // Helper method to emit loaded state
  void emitLoaded<T>(Emitter<State> emit, T data) {
    emit(LoadedState<T>(data) as State);
  }
  
  // Helper method to emit empty state
  void emitEmpty(Emitter<State> emit) {
    emit(const EmptyState() as State);
  }
  
  // Helper method to emit submitted state
  void emitSubmitted<T>(Emitter<State> emit, {T? data, String? message}) {
    emit(SubmittedState<T>(data: data, message: message) as State);
  }
  
  // Helper method to emit submitting state
  void emitSubmitting(Emitter<State> emit) {
    emit(const SubmittingState() as State);
  }
}

// Mixin for pagination functionality
mixin PaginationMixin<Event extends BaseEvent, State extends BaseState> 
    on BaseBloc<Event, State> {
  
  int currentPage = 1;
  int pageSize = 20;
  bool hasReachedMax = false;
  List<dynamic> items = [];
  
  void resetPagination() {
    currentPage = 1;
    hasReachedMax = false;
    items.clear();
  }
  
  void updatePagination(List<dynamic> newItems) {
    if (newItems.length < pageSize) {
      hasReachedMax = true;
    }
    
    if (currentPage == 1) {
      items = newItems;
    } else {
      items.addAll(newItems);
    }
    
    currentPage++;
  }
  
  void emitPaginationLoading(Emitter<State> emit) {
    emit(const PaginationLoadingState() as State);
  }
  
  void emitPaginationLoaded<T>(Emitter<State> emit, List<T> items) {
    emit(PaginationLoadedState<T>(
      items: items,
      hasReachedMax: hasReachedMax,
    ) as State);
  }
}

// Mixin for search functionality
mixin SearchMixin<Event extends BaseEvent, State extends BaseState> 
    on BaseBloc<Event, State> {
  
  String currentQuery = '';
  List<dynamic> searchResults = [];
  
  void updateSearch(String query, List<dynamic> results) {
    currentQuery = query;
    searchResults = results;
  }
  
  void clearSearch() {
    currentQuery = '';
    searchResults.clear();
  }
  
  void emitSearching(Emitter<State> emit) {
    emit(const SearchingState() as State);
  }
  
  void emitSearchResults<T>(Emitter<State> emit, List<T> results, String query) {
    emit(SearchResultState<T>(
      results: results,
      query: query,
    ) as State);
  }
}

// Mixin for file upload functionality
mixin FileUploadMixin<Event extends BaseEvent, State extends BaseState> 
    on BaseBloc<Event, State> {
  
  double uploadProgress = 0.0;
  
  void updateUploadProgress(double progress) {
    uploadProgress = progress;
  }
  
  void resetUploadProgress() {
    uploadProgress = 0.0;
  }
  
  void emitUploading(Emitter<State> emit, double progress) {
    emit(UploadingState(progress) as State);
  }
  
  void emitUploaded(Emitter<State> emit, {String? url, String? message}) {
    emit(UploadedState(url: url, message: message) as State);
  }
}

// Mixin for caching functionality
mixin CacheMixin<Event extends BaseEvent, State extends BaseState> 
    on BaseBloc<Event, State> {
  
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Duration cacheExpiration = const Duration(minutes: 5);
  
  void cacheData(String key, dynamic data) {
    _cache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
  }
  
  T? getCachedData<T>(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return null;
    
    final isExpired = DateTime.now().difference(timestamp) > cacheExpiration;
    if (isExpired) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
      return null;
    }
    
    return _cache[key] as T?;
  }
  
  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }
  
  void removeCachedData(String key) {
    _cache.remove(key);
    _cacheTimestamps.remove(key);
  }
}
