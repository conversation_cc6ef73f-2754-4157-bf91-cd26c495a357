import 'package:firebase_auth/firebase_auth.dart';
import 'package:medicare_app/core/base/base_event.dart';
import 'package:medicare_app/features/authentication/data/models/user_model.dart';

abstract class AuthEvent extends BaseEvent {
  const AuthEvent();
}

class AuthCheckStatusEvent extends AuthEvent {
  const AuthCheckStatusEvent();
}

class AuthLoginEvent extends AuthEvent {
  final String email;
  final String password;

  const AuthLoginEvent({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];
}

class AuthRegisterEvent extends AuthEvent {
  final Map<String, dynamic> userData;

  const AuthRegisterEvent({
    required this.userData,
  });

  @override
  List<Object?> get props => [userData];
}

class AuthLogoutEvent extends AuthEvent {
  const AuthLogoutEvent();
}

class AuthForgotPasswordEvent extends AuthEvent {
  final String email;

  const AuthForgotPasswordEvent({
    required this.email,
  });

  @override
  List<Object?> get props => [email];
}

class AuthChangePasswordEvent extends AuthEvent {
  final String currentPassword;
  final String newPassword;

  const AuthChangePasswordEvent({
    required this.currentPassword,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [currentPassword, newPassword];
}

class AuthUpdateProfileEvent extends AuthEvent {
  final UserModel user;

  const AuthUpdateProfileEvent({
    required this.user,
  });

  @override
  List<Object?> get props => [user];
}

class AuthDeleteAccountEvent extends AuthEvent {
  const AuthDeleteAccountEvent();
}

class AuthSendEmailVerificationEvent extends AuthEvent {
  const AuthSendEmailVerificationEvent();
}

class AuthStateChangedEvent extends AuthEvent {
  final User? user;

  const AuthStateChangedEvent(this.user);

  @override
  List<Object?> get props => [user];
}

class AuthRefreshUserEvent extends AuthEvent {
  const AuthRefreshUserEvent();
}

class AuthVerifyEmailEvent extends AuthEvent {
  const AuthVerifyEmailEvent();
}

class AuthResendVerificationEvent extends AuthEvent {
  const AuthResendVerificationEvent();
}

// Social authentication events
class AuthGoogleSignInEvent extends AuthEvent {
  const AuthGoogleSignInEvent();
}

class AuthAppleSignInEvent extends AuthEvent {
  const AuthAppleSignInEvent();
}

class AuthFacebookSignInEvent extends AuthEvent {
  const AuthFacebookSignInEvent();
}

// Phone authentication events
class AuthSendPhoneVerificationEvent extends AuthEvent {
  final String phoneNumber;

  const AuthSendPhoneVerificationEvent({
    required this.phoneNumber,
  });

  @override
  List<Object?> get props => [phoneNumber];
}

class AuthVerifyPhoneEvent extends AuthEvent {
  final String verificationId;
  final String smsCode;

  const AuthVerifyPhoneEvent({
    required this.verificationId,
    required this.smsCode,
  });

  @override
  List<Object?> get props => [verificationId, smsCode];
}

// Biometric authentication events
class AuthEnableBiometricEvent extends AuthEvent {
  const AuthEnableBiometricEvent();
}

class AuthDisableBiometricEvent extends AuthEvent {
  const AuthDisableBiometricEvent();
}

class AuthBiometricSignInEvent extends AuthEvent {
  const AuthBiometricSignInEvent();
}

// Two-factor authentication events
class AuthEnable2FAEvent extends AuthEvent {
  const AuthEnable2FAEvent();
}

class AuthDisable2FAEvent extends AuthEvent {
  const AuthDisable2FAEvent();
}

class AuthVerify2FAEvent extends AuthEvent {
  final String code;

  const AuthVerify2FAEvent({
    required this.code,
  });

  @override
  List<Object?> get props => [code];
}

// Account linking events
class AuthLinkAccountEvent extends AuthEvent {
  final String provider;
  final Map<String, dynamic> credentials;

  const AuthLinkAccountEvent({
    required this.provider,
    required this.credentials,
  });

  @override
  List<Object?> get props => [provider, credentials];
}

class AuthUnlinkAccountEvent extends AuthEvent {
  final String provider;

  const AuthUnlinkAccountEvent({
    required this.provider,
  });

  @override
  List<Object?> get props => [provider];
}

// Session management events
class AuthExtendSessionEvent extends AuthEvent {
  const AuthExtendSessionEvent();
}

class AuthCheckSessionEvent extends AuthEvent {
  const AuthCheckSessionEvent();
}

class AuthInvalidateSessionEvent extends AuthEvent {
  const AuthInvalidateSessionEvent();
}

// Security events
class AuthUpdateSecuritySettingsEvent extends AuthEvent {
  final Map<String, dynamic> settings;

  const AuthUpdateSecuritySettingsEvent({
    required this.settings,
  });

  @override
  List<Object?> get props => [settings];
}

class AuthReportSecurityIncidentEvent extends AuthEvent {
  final String incidentType;
  final Map<String, dynamic> details;

  const AuthReportSecurityIncidentEvent({
    required this.incidentType,
    required this.details,
  });

  @override
  List<Object?> get props => [incidentType, details];
}

// Device management events
class AuthRegisterDeviceEvent extends AuthEvent {
  final Map<String, dynamic> deviceInfo;

  const AuthRegisterDeviceEvent({
    required this.deviceInfo,
  });

  @override
  List<Object?> get props => [deviceInfo];
}

class AuthUnregisterDeviceEvent extends AuthEvent {
  final String deviceId;

  const AuthUnregisterDeviceEvent({
    required this.deviceId,
  });

  @override
  List<Object?> get props => [deviceId];
}

class AuthListDevicesEvent extends AuthEvent {
  const AuthListDevicesEvent();
}

// Privacy events
class AuthUpdatePrivacySettingsEvent extends AuthEvent {
  final Map<String, dynamic> settings;

  const AuthUpdatePrivacySettingsEvent({
    required this.settings,
  });

  @override
  List<Object?> get props => [settings];
}

class AuthExportDataEvent extends AuthEvent {
  const AuthExportDataEvent();
}

class AuthDeleteDataEvent extends AuthEvent {
  final List<String> dataTypes;

  const AuthDeleteDataEvent({
    required this.dataTypes,
  });

  @override
  List<Object?> get props => [dataTypes];
}
