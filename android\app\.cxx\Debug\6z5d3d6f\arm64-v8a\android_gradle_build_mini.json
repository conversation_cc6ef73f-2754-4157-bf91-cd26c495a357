{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutter_app2\\android\\app\\.cxx\\Debug\\6z5d3d6f\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutter_app2\\android\\app\\.cxx\\Debug\\6z5d3d6f\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}