import 'package:flutter/material.dart';
import 'package:medicare_app/core/constants/app_colors.dart';
import 'package:medicare_app/core/config/api_config.dart';
import 'package:medicare_app/core/services/gemini_service.dart';

class ApiSettingsScreen extends StatefulWidget {
  const ApiSettingsScreen({super.key});

  @override
  State<ApiSettingsScreen> createState() => _ApiSettingsScreenState();
}

class _ApiSettingsScreenState extends State<ApiSettingsScreen> {
  final _geminiController = TextEditingController();
  final _openaiController = TextEditingController();
  final _agoraController = TextEditingController();
  final _stripeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  void _loadCurrentSettings() {
    _geminiController.text = ApiConfig.geminiApiKey;
    _openaiController.text = ApiConfig.openaiApiKey;
    _agoraController.text = ApiConfig.agoraAppId;
    _stripeController.text = ApiConfig.stripePublishableKey;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        title: const Text('إعدادات API'),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showApiInfo,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.api,
                    size: 48,
                    color: AppColors.textOnPrimary,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'تكوين مفاتيح API',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textOnPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'قم بتكوين مفاتيح API لتفعيل الميزات المتقدمة',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textOnPrimary.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Configuration Status
            _buildConfigurationStatus(),
            const SizedBox(height: 24),

            // API Keys Configuration
            _buildApiKeySection(
              title: 'Google Gemini AI',
              subtitle: 'للمساعد الطبي الذكي وتحليل الأعراض',
              controller: _geminiController,
              icon: Icons.psychology,
              isConfigured: ApiConfig.isGeminiConfigured,
              helpText: 'احصل على مفتاح API من Google AI Studio',
            ),
            const SizedBox(height: 16),

            _buildApiKeySection(
              title: 'OpenAI',
              subtitle: 'للميزات الإضافية للذكاء الاصطناعي',
              controller: _openaiController,
              icon: Icons.smart_toy,
              isConfigured: ApiConfig.isOpenAIConfigured,
              helpText: 'احصل على مفتاح API من OpenAI Platform',
            ),
            const SizedBox(height: 16),

            _buildApiKeySection(
              title: 'Agora Video',
              subtitle: 'للمكالمات المرئية والصوتية',
              controller: _agoraController,
              icon: Icons.videocam,
              isConfigured: ApiConfig.isAgoraConfigured,
              helpText: 'احصل على App ID من Agora Console',
            ),
            const SizedBox(height: 16),

            _buildApiKeySection(
              title: 'Stripe Payments',
              subtitle: 'لمعالجة المدفوعات',
              controller: _stripeController,
              icon: Icons.payment,
              isConfigured: ApiConfig.isStripeConfigured,
              helpText: 'احصل على Publishable Key من Stripe Dashboard',
            ),
            const SizedBox(height: 24),

            // Test API Connection
            _buildTestSection(),
            const SizedBox(height: 24),

            // Save Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saveSettings,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.textOnPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'حفظ الإعدادات',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Warning
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.warning),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: AppColors.warning),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'تنبيه: مفاتيح API حساسة ويجب حمايتها. لا تشاركها مع أي شخص.',
                      style: TextStyle(
                        color: AppColors.warning,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationStatus() {
    final status = ApiConfig.getConfigurationStatus();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'حالة التكوين',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          
          ...status.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(
                    entry.value ? Icons.check_circle : Icons.cancel,
                    color: entry.value ? AppColors.success : AppColors.error,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      entry.key,
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  Text(
                    entry.value ? 'مُكوَّن' : 'غير مُكوَّن',
                    style: TextStyle(
                      color: entry.value ? AppColors.success : AppColors.error,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildApiKeySection({
    required String title,
    required String subtitle,
    required TextEditingController controller,
    required IconData icon,
    required bool isConfigured,
    required String helpText,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isConfigured ? AppColors.success : AppColors.border,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isConfigured ? AppColors.success : AppColors.error,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  isConfigured ? 'مُكوَّن' : 'غير مُكوَّن',
                  style: TextStyle(
                    color: AppColors.textOnPrimary,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          TextField(
            controller: controller,
            decoration: InputDecoration(
              hintText: 'أدخل مفتاح API...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.primary),
              ),
              suffixIcon: IconButton(
                icon: const Icon(Icons.visibility),
                onPressed: () {
                  // Toggle visibility
                },
              ),
            ),
            obscureText: true,
          ),
          const SizedBox(height: 8),
          
          Text(
            helpText,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختبار الاتصال',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اختبر اتصال API للتأكد من صحة التكوين',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 16),
          
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: _testGeminiConnection,
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: AppColors.primary),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'اختبار Gemini AI',
                style: TextStyle(color: AppColors.primary),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _saveSettings() {
    // In a real app, you would save these to secure storage
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('تم حفظ الإعدادات بنجاح'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _testGeminiConnection() async {
    if (!ApiConfig.isGeminiConfigured) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('يرجى تكوين مفتاح Gemini API أولاً'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    try {
      final response = await GeminiService.getMedicalAdvice('مرحبا');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم الاتصال بـ Gemini AI بنجاح!'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل الاتصال: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showApiInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات API'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Google Gemini AI:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• انتقل إلى Google AI Studio\n• أنشئ مشروع جديد\n• احصل على API Key'),
              SizedBox(height: 16),
              
              Text(
                'OpenAI:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• انتقل إلى OpenAI Platform\n• أنشئ حساب\n• احصل على API Key'),
              SizedBox(height: 16),
              
              Text(
                'Agora:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• انتقل إلى Agora Console\n• أنشئ مشروع\n• احصل على App ID'),
              SizedBox(height: 16),
              
              Text(
                'Stripe:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• انتقل إلى Stripe Dashboard\n• احصل على Publishable Key'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _geminiController.dispose();
    _openaiController.dispose();
    _agoraController.dispose();
    _stripeController.dispose();
    super.dispose();
  }
}
