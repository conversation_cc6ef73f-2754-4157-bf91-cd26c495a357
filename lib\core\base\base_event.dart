import 'package:equatable/equatable.dart';

abstract class BaseEvent extends Equatable {
  const BaseEvent();
  
  @override
  List<Object?> get props => [];
}

// Generic events
class LoadEvent extends BaseEvent {
  const LoadEvent();
}

class RefreshEvent extends BaseEvent {
  const RefreshEvent();
}

class ResetEvent extends BaseEvent {
  const ResetEvent();
}

class RetryEvent extends BaseEvent {
  const RetryEvent();
}

// Data manipulation events
class CreateEvent<T> extends BaseEvent {
  final T data;
  
  const CreateEvent(this.data);
  
  @override
  List<Object?> get props => [data];
}

class UpdateEvent<T> extends BaseEvent {
  final String id;
  final T data;
  
  const UpdateEvent({
    required this.id,
    required this.data,
  });
  
  @override
  List<Object?> get props => [id, data];
}

class DeleteEvent extends BaseEvent {
  final String id;
  
  const DeleteEvent(this.id);
  
  @override
  List<Object?> get props => [id];
}

class GetByIdEvent extends BaseEvent {
  final String id;
  
  const GetByIdEvent(this.id);
  
  @override
  List<Object?> get props => [id];
}

// Pagination events
class LoadMoreEvent extends BaseEvent {
  const LoadMoreEvent();
}

class ResetPaginationEvent extends BaseEvent {
  const ResetPaginationEvent();
}

// Search events
class SearchEvent extends BaseEvent {
  final String query;
  
  const SearchEvent(this.query);
  
  @override
  List<Object?> get props => [query];
}

class ClearSearchEvent extends BaseEvent {
  const ClearSearchEvent();
}

// Filter and sort events
class FilterEvent extends BaseEvent {
  final Map<String, dynamic> filters;
  
  const FilterEvent(this.filters);
  
  @override
  List<Object?> get props => [filters];
}

class SortEvent extends BaseEvent {
  final String sortBy;
  final bool ascending;
  
  const SortEvent({
    required this.sortBy,
    this.ascending = true,
  });
  
  @override
  List<Object?> get props => [sortBy, ascending];
}

class ClearFiltersEvent extends BaseEvent {
  const ClearFiltersEvent();
}

// File upload events
class UploadFileEvent extends BaseEvent {
  final String filePath;
  final String? fileName;
  final Map<String, dynamic>? metadata;
  
  const UploadFileEvent({
    required this.filePath,
    this.fileName,
    this.metadata,
  });
  
  @override
  List<Object?> get props => [filePath, fileName, metadata];
}

class UploadMultipleFilesEvent extends BaseEvent {
  final List<String> filePaths;
  final Map<String, dynamic>? metadata;
  
  const UploadMultipleFilesEvent({
    required this.filePaths,
    this.metadata,
  });
  
  @override
  List<Object?> get props => [filePaths, metadata];
}

class CancelUploadEvent extends BaseEvent {
  const CancelUploadEvent();
}

// Download events
class DownloadFileEvent extends BaseEvent {
  final String url;
  final String savePath;
  
  const DownloadFileEvent({
    required this.url,
    required this.savePath,
  });
  
  @override
  List<Object?> get props => [url, savePath];
}

class CancelDownloadEvent extends BaseEvent {
  const CancelDownloadEvent();
}

// Authentication events
class LoginEvent extends BaseEvent {
  final String email;
  final String password;
  
  const LoginEvent({
    required this.email,
    required this.password,
  });
  
  @override
  List<Object?> get props => [email, password];
}

class RegisterEvent extends BaseEvent {
  final Map<String, dynamic> userData;
  
  const RegisterEvent(this.userData);
  
  @override
  List<Object?> get props => [userData];
}

class LogoutEvent extends BaseEvent {
  const LogoutEvent();
}

class ForgotPasswordEvent extends BaseEvent {
  final String email;
  
  const ForgotPasswordEvent(this.email);
  
  @override
  List<Object?> get props => [email];
}

class ResetPasswordEvent extends BaseEvent {
  final String token;
  final String newPassword;
  
  const ResetPasswordEvent({
    required this.token,
    required this.newPassword,
  });
  
  @override
  List<Object?> get props => [token, newPassword];
}

class ChangePasswordEvent extends BaseEvent {
  final String currentPassword;
  final String newPassword;
  
  const ChangePasswordEvent({
    required this.currentPassword,
    required this.newPassword,
  });
  
  @override
  List<Object?> get props => [currentPassword, newPassword];
}

class CheckAuthStatusEvent extends BaseEvent {
  const CheckAuthStatusEvent();
}

// Permission events
class RequestPermissionEvent extends BaseEvent {
  final String permission;
  
  const RequestPermissionEvent(this.permission);
  
  @override
  List<Object?> get props => [permission];
}

class CheckPermissionEvent extends BaseEvent {
  final String permission;
  
  const CheckPermissionEvent(this.permission);
  
  @override
  List<Object?> get props => [permission];
}

class OpenAppSettingsEvent extends BaseEvent {
  const OpenAppSettingsEvent();
}

// Network events
class CheckNetworkEvent extends BaseEvent {
  const CheckNetworkEvent();
}

class NetworkStatusChangedEvent extends BaseEvent {
  final bool isConnected;
  
  const NetworkStatusChangedEvent(this.isConnected);
  
  @override
  List<Object?> get props => [isConnected];
}

// Video call events
class StartVideoCallEvent extends BaseEvent {
  final String channelId;
  final String? token;
  
  const StartVideoCallEvent({
    required this.channelId,
    this.token,
  });
  
  @override
  List<Object?> get props => [channelId, token];
}

class EndVideoCallEvent extends BaseEvent {
  const EndVideoCallEvent();
}

class ToggleAudioEvent extends BaseEvent {
  const ToggleAudioEvent();
}

class ToggleVideoEvent extends BaseEvent {
  const ToggleVideoEvent();
}

class SwitchCameraEvent extends BaseEvent {
  const SwitchCameraEvent();
}

class UserJoinedEvent extends BaseEvent {
  final int userId;
  
  const UserJoinedEvent(this.userId);
  
  @override
  List<Object?> get props => [userId];
}

class UserLeftEvent extends BaseEvent {
  final int userId;
  
  const UserLeftEvent(this.userId);
  
  @override
  List<Object?> get props => [userId];
}

// Notification events
class SendNotificationEvent extends BaseEvent {
  final String title;
  final String body;
  final Map<String, dynamic>? data;
  
  const SendNotificationEvent({
    required this.title,
    required this.body,
    this.data,
  });
  
  @override
  List<Object?> get props => [title, body, data];
}

class ScheduleNotificationEvent extends BaseEvent {
  final String title;
  final String body;
  final DateTime scheduledTime;
  final Map<String, dynamic>? data;
  
  const ScheduleNotificationEvent({
    required this.title,
    required this.body,
    required this.scheduledTime,
    this.data,
  });
  
  @override
  List<Object?> get props => [title, body, scheduledTime, data];
}

class CancelNotificationEvent extends BaseEvent {
  final int notificationId;
  
  const CancelNotificationEvent(this.notificationId);
  
  @override
  List<Object?> get props => [notificationId];
}
