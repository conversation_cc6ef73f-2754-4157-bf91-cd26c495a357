import 'package:flutter/material.dart';
import 'package:medicare_app/core/constants/app_colors.dart';
import 'package:medicare_app/core/config/api_config.dart';
import 'package:medicare_app/core/services/gemini_service.dart';

class ApiTestScreen extends StatefulWidget {
  const ApiTestScreen({super.key});

  @override
  State<ApiTestScreen> createState() => _ApiTestScreenState();
}

class _ApiTestScreenState extends State<ApiTestScreen> {
  String _testResult = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkApiConfiguration();
  }

  void _checkApiConfiguration() {
    setState(() {
      if (ApiConfig.isGeminiConfigured) {
        _testResult = '✅ مفتاح Gemini API مُكوَّن بشكل صحيح!\n\n';
        _testResult += 'المفتاح: ${ApiConfig.geminiApiKey.substring(0, 20)}...\n\n';
        _testResult += 'جاهز لاختبار الاتصال...';
      } else {
        _testResult = '❌ مفتاح Gemini API غير مُكوَّن بشكل صحيح';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        title: const Text('اختبار Gemini API'),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.api,
                    size: 48,
                    color: AppColors.textOnPrimary,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'اختبار Google Gemini API',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textOnPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'تحقق من تكوين واتصال API',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textOnPrimary.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Configuration Status
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: ApiConfig.isGeminiConfigured 
                      ? AppColors.success 
                      : AppColors.error,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        ApiConfig.isGeminiConfigured 
                            ? Icons.check_circle 
                            : Icons.error,
                        color: ApiConfig.isGeminiConfigured 
                            ? AppColors.success 
                            : AppColors.error,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'حالة التكوين',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'مفتاح API: ${ApiConfig.isGeminiConfigured ? "مُكوَّن ✅" : "غير مُكوَّن ❌"}',
                    style: TextStyle(
                      color: AppColors.textPrimary,
                      fontSize: 14,
                    ),
                  ),
                  if (ApiConfig.isGeminiConfigured) ...[
                    const SizedBox(height: 8),
                    Text(
                      'المفتاح: ${ApiConfig.geminiApiKey.substring(0, 20)}...',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 12,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Test Results
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: AppColors.border),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'نتائج الاختبار',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: SingleChildScrollView(
                        child: _isLoading
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CircularProgressIndicator(
                                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'جاري اختبار الاتصال...',
                                      style: TextStyle(
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : Text(
                                _testResult,
                                style: TextStyle(
                                  color: AppColors.textPrimary,
                                  fontSize: 14,
                                  height: 1.5,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Test Buttons
            if (ApiConfig.isGeminiConfigured) ...[
              ElevatedButton(
                onPressed: _isLoading ? null : _testBasicConnection,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.textOnPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'اختبار الاتصال الأساسي',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
              const SizedBox(height: 12),
              ElevatedButton(
                onPressed: _isLoading ? null : _testMedicalQuestion,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.secondary,
                  foregroundColor: AppColors.textOnPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'اختبار سؤال طبي',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
              const SizedBox(height: 12),
              ElevatedButton(
                onPressed: _isLoading ? null : _testSymptomAnalysis,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.accent,
                  foregroundColor: AppColors.textOnPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'اختبار تحليل الأعراض',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.warning),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: AppColors.warning),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'يرجى تكوين مفتاح Gemini API أولاً في ملف api_config.dart',
                        style: TextStyle(
                          color: AppColors.warning,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        ),
      ),
    );
  }

  void _testBasicConnection() async {
    setState(() {
      _isLoading = true;
      _testResult += '\n\n🔄 اختبار الاتصال الأساسي...\n';
    });

    try {
      final response = await GeminiService.getMedicalAdvice('مرحبا');
      setState(() {
        _testResult += '✅ نجح الاتصال!\n';
        _testResult += 'الرد: ${response.substring(0, 100)}...\n';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _testResult += '❌ فشل الاتصال: $e\n';
        _isLoading = false;
      });
    }
  }

  void _testMedicalQuestion() async {
    setState(() {
      _isLoading = true;
      _testResult += '\n\n🔄 اختبار سؤال طبي...\n';
    });

    try {
      final response = await GeminiService.getMedicalAdvice('ما هي أعراض نزلات البرد؟');
      setState(() {
        _testResult += '✅ نجح الاختبار!\n';
        _testResult += 'السؤال: ما هي أعراض نزلات البرد؟\n';
        _testResult += 'الرد: ${response.substring(0, 200)}...\n';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _testResult += '❌ فشل الاختبار: $e\n';
        _isLoading = false;
      });
    }
  }

  void _testSymptomAnalysis() async {
    setState(() {
      _isLoading = true;
      _testResult += '\n\n🔄 اختبار تحليل الأعراض...\n';
    });

    try {
      final result = await GeminiService.analyzeSymptoms(
        symptoms: ['صداع', 'حمى'],
        age: '25',
        gender: 'ذكر',
      );
      setState(() {
        _testResult += '✅ نجح تحليل الأعراض!\n';
        _testResult += 'الأعراض: صداع، حمى\n';
        _testResult += 'مستوى الخطورة: ${result['severity']}\n';
        _testResult += 'التحليل متاح بالكامل في صفحة فحص الأعراض\n';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _testResult += '❌ فشل تحليل الأعراض: $e\n';
        _isLoading = false;
      });
    }
  }
}
