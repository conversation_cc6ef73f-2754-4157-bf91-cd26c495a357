class AppConstants {
  // App Information
  static const String appName = 'MediCare';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Comprehensive Medical Application';
  
  // API Configuration
  static const String baseUrl = 'https://api.medicare.com';
  static const String openAIBaseUrl = 'https://api.openai.com/v1';
  static const String stripeBaseUrl = 'https://api.stripe.com/v1';
  
  // Firebase Collections
  static const String usersCollection = 'users';
  static const String doctorsCollection = 'doctors';
  static const String patientsCollection = 'patients';
  static const String appointmentsCollection = 'appointments';
  static const String medicalRecordsCollection = 'medical_records';
  static const String prescriptionsCollection = 'prescriptions';
  static const String chatsCollection = 'chats';
  static const String messagesCollection = 'messages';
  static const String paymentsCollection = 'payments';
  
  // User Roles
  static const String patientRole = 'patient';
  static const String doctorRole = 'doctor';
  static const String adminRole = 'admin';
  
  // Appointment Status
  static const String appointmentPending = 'pending';
  static const String appointmentConfirmed = 'confirmed';
  static const String appointmentCompleted = 'completed';
  static const String appointmentCancelled = 'cancelled';
  
  // Payment Status
  static const String paymentPending = 'pending';
  static const String paymentCompleted = 'completed';
  static const String paymentFailed = 'failed';
  static const String paymentRefunded = 'refunded';
  
  // File Types
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];
  
  // Limits
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const int maxImagesPerUpload = 5;
  static const int chatMessageLimit = 100;
  
  // Time Constants
  static const int sessionTimeoutMinutes = 30;
  static const int appointmentReminderHours = 24;
  static const int medicationReminderMinutes = 15;
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 30;
  
  // Agora Configuration
  static const String agoraAppId = 'YOUR_AGORA_APP_ID';
  static const String agoraToken = 'YOUR_AGORA_TOKEN';
  
  // OpenAI Configuration
  static const String openAIModel = 'gpt-4';
  static const int maxTokens = 1000;
  static const double temperature = 0.7;
  
  // Error Messages
  static const String networkError = 'Network connection error';
  static const String serverError = 'Server error occurred';
  static const String authError = 'Authentication failed';
  static const String permissionError = 'Permission denied';
  static const String fileUploadError = 'File upload failed';
  
  // Success Messages
  static const String appointmentBooked = 'Appointment booked successfully';
  static const String profileUpdated = 'Profile updated successfully';
  static const String passwordChanged = 'Password changed successfully';
  static const String paymentCompleted = 'Payment completed successfully';
}
