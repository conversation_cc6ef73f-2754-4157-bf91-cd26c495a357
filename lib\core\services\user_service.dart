import 'package:shared_preferences/shared_preferences.dart';

class UserService {
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';
  static const String _userEmailKey = 'user_email';
  static const String _userRoleKey = 'user_role';
  static const String _userPhoneKey = 'user_phone';
  static const String _isLoggedInKey = 'is_logged_in';

  static UserService? _instance;
  static UserService get instance => _instance ??= UserService._();
  UserService._();

  SharedPreferences? _prefs;

  Future<void> _initPrefs() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Save user data after login/register
  Future<void> saveUserData({
    required String userId,
    required String userName,
    required String userEmail,
    required String userRole,
    String? userPhone,
  }) async {
    await _initPrefs();
    await _prefs!.setString(_userIdKey, userId);
    await _prefs!.setString(_userName<PERSON>ey, userName);
    await _prefs!.setString(_userEmailKey, userEmail);
    await _prefs!.setString(_userRoleKey, userRole);
    if (userPhone != null) {
      await _prefs!.setString(_userPhoneKey, userPhone);
    }
    await _prefs!.setBool(_isLoggedInKey, true);
  }

  // Get current user data
  Future<Map<String, String?>> getCurrentUser() async {
    await _initPrefs();
    return {
      'userId': _prefs!.getString(_userIdKey),
      'userName': _prefs!.getString(_userNameKey),
      'userEmail': _prefs!.getString(_userEmailKey),
      'userRole': _prefs!.getString(_userRoleKey),
      'userPhone': _prefs!.getString(_userPhoneKey),
    };
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    await _initPrefs();
    return _prefs!.getBool(_isLoggedInKey) ?? false;
  }

  // Get user name
  Future<String> getUserName() async {
    await _initPrefs();
    return _prefs!.getString(_userNameKey) ?? 'User';
  }

  // Get user role
  Future<String> getUserRole() async {
    await _initPrefs();
    return _prefs!.getString(_userRoleKey) ?? 'patient';
  }

  // Get user email
  Future<String> getUserEmail() async {
    await _initPrefs();
    return _prefs!.getString(_userEmailKey) ?? '';
  }

  // Logout user
  Future<void> logout() async {
    await _initPrefs();
    await _prefs!.clear();
  }

  // Update user name
  Future<void> updateUserName(String newName) async {
    await _initPrefs();
    await _prefs!.setString(_userNameKey, newName);
  }

  // Simulate user database
  static final Map<String, Map<String, dynamic>> _userDatabase = {
    '<EMAIL>': {
      'id': '1',
      'firstName': 'أحمد',
      'lastName': 'محمد',
      'email': '<EMAIL>',
      'role': 'patient',
      'phone': '+966501234567',
      'password': '123456',
    },
    '<EMAIL>': {
      'id': '2',
      'firstName': 'د. سارة',
      'lastName': 'أحمد',
      'email': '<EMAIL>',
      'role': 'doctor',
      'phone': '+966507654321',
      'password': '123456',
      'specialization': 'طب الأطفال',
      'experience': '10 سنوات',
    },
    '<EMAIL>': {
      'id': '3',
      'firstName': 'Dr. John',
      'lastName': 'Smith',
      'email': '<EMAIL>',
      'role': 'doctor',
      'phone': '+966509876543',
      'password': '123456',
      'specialization': 'Internal Medicine',
      'experience': '15 years',
    },
    '<EMAIL>': {
      'id': '4',
      'firstName': 'مدير',
      'lastName': 'النظام',
      'email': '<EMAIL>',
      'role': 'admin',
      'phone': '+966501111111',
      'password': '123456',
    },
  };

  // Authenticate user
  static Future<Map<String, dynamic>?> authenticateUser(String email, String password) async {
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay
    
    final user = _userDatabase[email.toLowerCase()];
    if (user != null && user['password'] == password) {
      return user;
    }
    return null;
  }

  // Register new user
  static Future<bool> registerUser(Map<String, dynamic> userData) async {
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay
    
    final email = userData['email'].toLowerCase();
    if (_userDatabase.containsKey(email)) {
      return false; // User already exists
    }
    
    // Add new user to database
    _userDatabase[email] = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'firstName': userData['firstName'],
      'lastName': userData['lastName'],
      'email': email,
      'role': userData['role'],
      'phone': userData['phone'],
      'password': userData['password'],
      'dateOfBirth': userData['dateOfBirth'],
    };
    
    return true;
  }

  // Get all users (for admin)
  static List<Map<String, dynamic>> getAllUsers() {
    return _userDatabase.values.toList();
  }

  // Get doctors list
  static List<Map<String, dynamic>> getDoctors() {
    return _userDatabase.values
        .where((user) => user['role'] == 'doctor')
        .toList();
  }

  // Get patients list
  static List<Map<String, dynamic>> getPatients() {
    return _userDatabase.values
        .where((user) => user['role'] == 'patient')
        .toList();
  }
}
