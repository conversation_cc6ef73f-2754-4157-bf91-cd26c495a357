import 'package:equatable/equatable.dart';
import 'package:medicare_app/core/errors/failures.dart';

abstract class BaseState extends Equatable {
  const BaseState();
  
  @override
  List<Object?> get props => [];
}

class InitialState extends BaseState {
  const InitialState();
}

class LoadingState extends BaseState {
  const LoadingState();
}

class LoadedState<T> extends BaseState {
  final T data;
  
  const LoadedState(this.data);
  
  @override
  List<Object?> get props => [data];
}

class ErrorState extends BaseState {
  final Failure failure;
  
  const ErrorState(this.failure);
  
  @override
  List<Object?> get props => [failure];
}

class EmptyState extends BaseState {
  const EmptyState();
}

// Specific states for different operations
class SubmittingState extends BaseState {
  const SubmittingState();
}

class SubmittedState<T> extends BaseState {
  final T? data;
  final String? message;
  
  const SubmittedState({this.data, this.message});
  
  @override
  List<Object?> get props => [data, message];
}

class RefreshingState extends BaseState {
  const RefreshingState();
}

class RefreshedState<T> extends BaseState {
  final T data;
  
  const RefreshedState(this.data);
  
  @override
  List<Object?> get props => [data];
}

class PaginationLoadingState extends BaseState {
  const PaginationLoadingState();
}

class PaginationLoadedState<T> extends BaseState {
  final List<T> items;
  final bool hasReachedMax;
  
  const PaginationLoadedState({
    required this.items,
    required this.hasReachedMax,
  });
  
  @override
  List<Object?> get props => [items, hasReachedMax];
}

class SearchingState extends BaseState {
  const SearchingState();
}

class SearchResultState<T> extends BaseState {
  final List<T> results;
  final String query;
  
  const SearchResultState({
    required this.results,
    required this.query,
  });
  
  @override
  List<Object?> get props => [results, query];
}

class UploadingState extends BaseState {
  final double progress;
  
  const UploadingState(this.progress);
  
  @override
  List<Object?> get props => [progress];
}

class UploadedState extends BaseState {
  final String? url;
  final String? message;
  
  const UploadedState({this.url, this.message});
  
  @override
  List<Object?> get props => [url, message];
}

class DownloadingState extends BaseState {
  final double progress;
  
  const DownloadingState(this.progress);
  
  @override
  List<Object?> get props => [progress];
}

class DownloadedState extends BaseState {
  final String filePath;
  
  const DownloadedState(this.filePath);
  
  @override
  List<Object?> get props => [filePath];
}

// Authentication specific states
class AuthenticatedState extends BaseState {
  final String userId;
  final String userRole;
  
  const AuthenticatedState({
    required this.userId,
    required this.userRole,
  });
  
  @override
  List<Object?> get props => [userId, userRole];
}

class UnauthenticatedState extends BaseState {
  const UnauthenticatedState();
}

class AuthenticationLoadingState extends BaseState {
  const AuthenticationLoadingState();
}

// Network specific states
class NetworkConnectedState extends BaseState {
  const NetworkConnectedState();
}

class NetworkDisconnectedState extends BaseState {
  const NetworkDisconnectedState();
}

class NetworkReconnectingState extends BaseState {
  const NetworkReconnectingState();
}

// Permission specific states
class PermissionGrantedState extends BaseState {
  final String permission;
  
  const PermissionGrantedState(this.permission);
  
  @override
  List<Object?> get props => [permission];
}

class PermissionDeniedState extends BaseState {
  final String permission;
  
  const PermissionDeniedState(this.permission);
  
  @override
  List<Object?> get props => [permission];
}

class PermissionPermanentlyDeniedState extends BaseState {
  final String permission;
  
  const PermissionPermanentlyDeniedState(this.permission);
  
  @override
  List<Object?> get props => [permission];
}

// Video call specific states
class VideoCallConnectingState extends BaseState {
  const VideoCallConnectingState();
}

class VideoCallConnectedState extends BaseState {
  final String channelId;
  final List<int> remoteUsers;
  
  const VideoCallConnectedState({
    required this.channelId,
    required this.remoteUsers,
  });
  
  @override
  List<Object?> get props => [channelId, remoteUsers];
}

class VideoCallDisconnectedState extends BaseState {
  const VideoCallDisconnectedState();
}

class VideoCallErrorState extends BaseState {
  final String error;
  
  const VideoCallErrorState(this.error);
  
  @override
  List<Object?> get props => [error];
}
