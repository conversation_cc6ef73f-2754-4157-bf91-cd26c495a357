import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:medicare_app/core/constants/app_constants.dart';
import 'package:medicare_app/core/errors/exceptions.dart';
import 'package:medicare_app/features/authentication/data/models/user_model.dart';
import 'package:medicare_app/features/authentication/data/models/doctor_model.dart';

abstract class AuthRepository {
  Future<UserModel> signInWithEmailAndPassword(String email, String password);
  Future<UserModel> registerWithEmailAndPassword(Map<String, dynamic> userData);
  Future<void> signOut();
  Future<void> sendPasswordResetEmail(String email);
  Future<void> changePassword(String currentPassword, String newPassword);
  Future<UserModel?> getCurrentUser();
  Future<void> updateUserProfile(UserModel user);
  Future<void> deleteAccount();
  Future<bool> isEmailVerified();
  Future<void> sendEmailVerification();
  Stream<User?> get authStateChanges;
}

class AuthRepositoryImpl implements AuthRepository {
  final FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;

  AuthRepositoryImpl({
    required FirebaseAuth firebaseAuth,
    required FirebaseFirestore firestore,
  })  : _firebaseAuth = firebaseAuth,
        _firestore = firestore;

  @override
  Future<UserModel> signInWithEmailAndPassword(String email, String password) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        throw const AuthenticationException(message: 'Sign in failed');
      }

      final user = await _getUserFromFirestore(credential.user!.uid);
      if (user == null) {
        throw const AuthenticationException(message: 'User data not found');
      }

      return user;
    } on FirebaseAuthException catch (e) {
      throw AuthenticationException(
        message: _getAuthErrorMessage(e.code),
        code: e.code,
      );
    } catch (e) {
      throw AuthenticationException(message: e.toString());
    }
  }

  @override
  Future<UserModel> registerWithEmailAndPassword(Map<String, dynamic> userData) async {
    try {
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: userData['email'],
        password: userData['password'],
      );

      if (credential.user == null) {
        throw const AuthenticationException(message: 'Registration failed');
      }

      // Create user document in Firestore
      final user = await _createUserDocument(credential.user!.uid, userData);
      
      // Send email verification
      await credential.user!.sendEmailVerification();

      return user;
    } on FirebaseAuthException catch (e) {
      throw AuthenticationException(
        message: _getAuthErrorMessage(e.code),
        code: e.code,
      );
    } catch (e) {
      throw AuthenticationException(message: e.toString());
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
    } catch (e) {
      throw AuthenticationException(message: e.toString());
    }
  }

  @override
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw AuthenticationException(
        message: _getAuthErrorMessage(e.code),
        code: e.code,
      );
    } catch (e) {
      throw AuthenticationException(message: e.toString());
    }
  }

  @override
  Future<void> changePassword(String currentPassword, String newPassword) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw const AuthenticationException(message: 'No user signed in');
      }

      // Re-authenticate user
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );
      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(newPassword);
    } on FirebaseAuthException catch (e) {
      throw AuthenticationException(
        message: _getAuthErrorMessage(e.code),
        code: e.code,
      );
    } catch (e) {
      throw AuthenticationException(message: e.toString());
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final firebaseUser = _firebaseAuth.currentUser;
      if (firebaseUser == null) return null;

      return await _getUserFromFirestore(firebaseUser.uid);
    } catch (e) {
      throw AuthenticationException(message: e.toString());
    }
  }

  @override
  Future<void> updateUserProfile(UserModel user) async {
    try {
      final userDoc = _firestore.collection(AppConstants.usersCollection).doc(user.id);
      
      final updatedUser = user.copyWith(updatedAt: DateTime.now());
      await userDoc.update(updatedUser.toFirestore());

      // Update Firebase Auth profile if needed
      final firebaseUser = _firebaseAuth.currentUser;
      if (firebaseUser != null) {
        await firebaseUser.updateDisplayName(user.fullName);
      }
    } catch (e) {
      throw AuthenticationException(message: e.toString());
    }
  }

  @override
  Future<void> deleteAccount() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw const AuthenticationException(message: 'No user signed in');
      }

      // Delete user document from Firestore
      await _firestore.collection(AppConstants.usersCollection).doc(user.uid).delete();

      // Delete Firebase Auth account
      await user.delete();
    } on FirebaseAuthException catch (e) {
      throw AuthenticationException(
        message: _getAuthErrorMessage(e.code),
        code: e.code,
      );
    } catch (e) {
      throw AuthenticationException(message: e.toString());
    }
  }

  @override
  Future<bool> isEmailVerified() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) return false;

      await user.reload();
      return user.emailVerified;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> sendEmailVerification() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw const AuthenticationException(message: 'No user signed in');
      }

      await user.sendEmailVerification();
    } on FirebaseAuthException catch (e) {
      throw AuthenticationException(
        message: _getAuthErrorMessage(e.code),
        code: e.code,
      );
    } catch (e) {
      throw AuthenticationException(message: e.toString());
    }
  }

  @override
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  // Private helper methods
  Future<UserModel?> _getUserFromFirestore(String uid) async {
    try {
      final doc = await _firestore.collection(AppConstants.usersCollection).doc(uid).get();
      
      if (!doc.exists) return null;

      final data = doc.data()!;
      final role = data['role'] ?? AppConstants.patientRole;

      if (role == AppConstants.doctorRole) {
        return DoctorModel.fromFirestore(doc);
      } else {
        return UserModel.fromFirestore(doc);
      }
    } catch (e) {
      throw FirestoreException(message: e.toString());
    }
  }

  Future<UserModel> _createUserDocument(String uid, Map<String, dynamic> userData) async {
    try {
      final now = DateTime.now();
      final role = userData['role'] ?? AppConstants.patientRole;

      UserModel user;
      
      if (role == AppConstants.doctorRole) {
        user = DoctorModel(
          id: uid,
          email: userData['email'],
          firstName: userData['firstName'],
          lastName: userData['lastName'],
          phoneNumber: userData['phoneNumber'],
          dateOfBirth: userData['dateOfBirth'],
          gender: userData['gender'],
          createdAt: now,
          updatedAt: now,
          medicalLicenseNumber: userData['medicalLicenseNumber'],
          specialization: userData['specialization'],
          yearsOfExperience: userData['yearsOfExperience'],
          education: userData['education'],
          consultationFee: userData['consultationFee'],
        );
      } else {
        user = UserModel(
          id: uid,
          email: userData['email'],
          firstName: userData['firstName'],
          lastName: userData['lastName'],
          phoneNumber: userData['phoneNumber'],
          dateOfBirth: userData['dateOfBirth'],
          gender: userData['gender'],
          role: role,
          createdAt: now,
          updatedAt: now,
        );
      }

      await _firestore.collection(AppConstants.usersCollection).doc(uid).set(user.toFirestore());
      
      return user;
    } catch (e) {
      throw FirestoreException(message: e.toString());
    }
  }

  String _getAuthErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No user found with this email address';
      case 'wrong-password':
        return 'Incorrect password';
      case 'email-already-in-use':
        return 'An account already exists with this email address';
      case 'weak-password':
        return 'Password is too weak';
      case 'invalid-email':
        return 'Invalid email address';
      case 'user-disabled':
        return 'This account has been disabled';
      case 'too-many-requests':
        return 'Too many requests. Please try again later';
      case 'operation-not-allowed':
        return 'This operation is not allowed';
      case 'requires-recent-login':
        return 'Please sign in again to complete this action';
      default:
        return 'An authentication error occurred';
    }
  }
}
