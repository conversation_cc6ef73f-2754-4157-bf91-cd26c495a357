import 'package:flutter/material.dart';
import 'package:medicare_app/core/constants/app_colors.dart';
import 'package:medicare_app/core/widgets/card_3d.dart';

void main() {
  runApp(const TestCardsTextApp());
}

class TestCardsTextApp extends StatelessWidget {
  const TestCardsTextApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Cards Text',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Cairo',
      ),
      home: const TestCardsTextScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class TestCardsTextScreen extends StatelessWidget {
  const TestCardsTextScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        title: Row(
          children: [
            Icon(Icons.text_fields, size: 24),
            const SizedBox(width: 8),
            const Text('اختبار النصوص المحدثة'),
          ],
        ),
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 48,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'النصوص المحدثة والمتناسقة',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'جميع البطاقات أصبحت بنصوص واضحة ومتناسقة',
                    style: TextStyle(
                      fontSize: 15,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 28),

            // Patient Action Cards
            Row(
              children: [
                Icon(Icons.person, color: AppColors.primary, size: 24),
                const SizedBox(width: 8),
                Text(
                  'بطاقات المرضى',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            SizedBox(
              height: 280,
              child: GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.1,
                children: [
                  ActionCard3D(
                    icon: Icons.calendar_today,
                    title: 'حجز موعد',
                    subtitle: 'احجز مع طبيبك المفضل',
                    gradient: AppColors.blueGradient,
                    onTap: () => _showMessage(context, 'حجز موعد'),
                  ),
                  ActionCard3D(
                    icon: Icons.psychology,
                    title: 'فحص الأعراض',
                    subtitle: 'تحليل ذكي للأعراض',
                    gradient: AppColors.purpleGradient,
                    onTap: () => _showMessage(context, 'فحص الأعراض'),
                  ),
                  ActionCard3D(
                    icon: Icons.smart_toy,
                    title: 'المساعد الطبي',
                    subtitle: 'استشارة طبية فورية',
                    gradient: AppColors.tealGradient,
                    onTap: () => _showMessage(context, 'المساعد الطبي'),
                  ),
                  ActionCard3D(
                    icon: Icons.folder_outlined,
                    title: 'السجلات الطبية',
                    subtitle: 'تاريخك الطبي الكامل',
                    gradient: AppColors.orangeGradient,
                    onTap: () => _showMessage(context, 'السجلات الطبية'),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 28),

            // Doctor Action Cards
            Row(
              children: [
                Icon(Icons.medical_services, color: AppColors.primary, size: 24),
                const SizedBox(width: 8),
                Text(
                  'بطاقات الأطباء',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            SizedBox(
              height: 280,
              child: GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.1,
                children: [
                  ActionCard3D(
                    icon: Icons.people,
                    title: 'قائمة المرضى',
                    subtitle: 'إدارة ومتابعة المرضى',
                    gradient: AppColors.greenGradient,
                    onTap: () => _showMessage(context, 'قائمة المرضى'),
                  ),
                  ActionCard3D(
                    icon: Icons.schedule,
                    title: 'جدول المواعيد',
                    subtitle: 'تنظيم وإدارة المواعيد',
                    gradient: AppColors.indigoGradient,
                    onTap: () => _showMessage(context, 'جدول المواعيد'),
                  ),
                  ActionCard3D(
                    icon: Icons.video_call,
                    title: 'الاستشارة المرئية',
                    subtitle: 'مكالمات فيديو طبية',
                    gradient: AppColors.redGradient,
                    onTap: () => _showMessage(context, 'الاستشارة المرئية'),
                  ),
                  ActionCard3D(
                    icon: Icons.analytics,
                    title: 'التقارير والإحصائيات',
                    subtitle: 'تحليل البيانات الطبية',
                    gradient: AppColors.cyanGradient,
                    onTap: () => _showMessage(context, 'التقارير والإحصائيات'),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 28),

            // Info Cards
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.primary, size: 24),
                const SizedBox(width: 8),
                Text(
                  'بطاقات المعلومات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            InfoCard3D(
              icon: Icons.check_circle,
              title: 'آخر موعد مكتمل',
              value: 'د. سارة أحمد • منذ ساعتين',
              gradient: AppColors.greenGradient,
              onTap: () => _showMessage(context, 'آخر موعد مكتمل'),
            ),
            const SizedBox(height: 12),
            
            InfoCard3D(
              icon: Icons.schedule,
              title: 'الموعد القادم',
              value: 'غداً • الساعة 10:00 صباحاً',
              gradient: AppColors.orangeGradient,
              onTap: () => _showMessage(context, 'الموعد القادم'),
            ),
            const SizedBox(height: 12),
            
            InfoCard3D(
              icon: Icons.medication,
              title: 'تذكير الأدوية',
              value: 'فيتامين د • الساعة 8:00 مساءً',
              gradient: AppColors.purpleGradient,
              onTap: () => _showMessage(context, 'تذكير الأدوية'),
            ),
            const SizedBox(height: 28),

            // Summary
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Icon(Icons.verified, color: AppColors.success, size: 32),
                  const SizedBox(height: 12),
                  Text(
                    'تم تحديث جميع النصوص بنجاح!',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.success,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '✅ نصوص واضحة ومفهومة\n'
                    '✅ تناسق في الأسلوب والطول\n'
                    '✅ معلومات مفيدة ودقيقة\n'
                    '✅ تجربة مستخدم محسنة',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textPrimary,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  static void _showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم النقر على: $message'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
