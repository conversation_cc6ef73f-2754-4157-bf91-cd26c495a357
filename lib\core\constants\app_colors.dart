import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2E7D8F);
  static const Color primaryLight = Color(0xFF5CADBD);
  static const Color primaryDark = Color(0xFF1A5A6B);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF4CAF50);
  static const Color secondaryLight = Color(0xFF81C784);
  static const Color secondaryDark = Color(0xFF388E3C);
  
  // Accent Colors
  static const Color accent = Color(0xFFFF6B6B);
  static const Color accentLight = Color(0xFFFF8A80);
  static const Color accentDark = Color(0xFFD32F2F);
  
  // Background Colors
  static const Color background = Color(0xFFF8F9FA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Medical Specific Colors
  static const Color doctorCard = Color(0xFFE3F2FD);
  static const Color patientCard = Color(0xFFE8F5E8);
  static const Color appointmentCard = Color(0xFFFFF3E0);
  static const Color prescriptionCard = Color(0xFFF3E5F5);
  
  // Appointment Status Colors
  static const Color appointmentPending = Color(0xFFFF9800);
  static const Color appointmentConfirmed = Color(0xFF4CAF50);
  static const Color appointmentCompleted = Color(0xFF2196F3);
  static const Color appointmentCancelled = Color(0xFFF44336);
  
  // Chat Colors
  static const Color chatBubbleUser = Color(0xFF2E7D8F);
  static const Color chatBubbleOther = Color(0xFFE0E0E0);
  static const Color chatBubbleDoctor = Color(0xFF4CAF50);
  static const Color chatBubbleAI = Color(0xFF9C27B0);
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderFocus = Color(0xFF2E7D8F);
  static const Color borderError = Color(0xFFF44336);
  
  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  static const Color shadowDark = Color(0x26000000);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [Color(0xFFF8F9FA), Color(0xFFE3F2FD)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  // Opacity Colors
  static Color primaryWithOpacity(double opacity) => primary.withValues(alpha: opacity);
  static Color secondaryWithOpacity(double opacity) => secondary.withValues(alpha: opacity);
  static Color errorWithOpacity(double opacity) => error.withValues(alpha: opacity);
  static Color successWithOpacity(double opacity) => success.withValues(alpha: opacity);
}
