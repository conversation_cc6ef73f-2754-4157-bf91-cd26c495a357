import 'package:flutter/material.dart';
import 'package:medicare_app/core/constants/app_colors.dart';
import 'package:medicare_app/core/constants/app_strings.dart';
import 'package:medicare_app/core/services/user_service.dart';
import 'package:medicare_app/core/widgets/card_3d.dart';

class AppointmentsScreen extends StatefulWidget {
  const AppointmentsScreen({super.key});

  @override
  State<AppointmentsScreen> createState() => _AppointmentsScreenState();
}

class _AppointmentsScreenState extends State<AppointmentsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String userRole = 'patient';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserRole();
  }

  Future<void> _loadUserRole() async {
    final role = await UserService.instance.getUserRole();
    setState(() {
      userRole = role;
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        title: Row(
          children: [
            Icon(Icons.calendar_today, size: 24),
            const SizedBox(width: 8),
            Text(userRole == 'patient' ? 'مواعيدي' : 'جدول المواعيد'),
          ],
        ),
        centerTitle: false,
        actions: [
          if (userRole == 'patient')
            Container(
              margin: const EdgeInsets.only(right: 8),
              child: IconButton(
                icon: const Icon(Icons.add_circle_outline),
                onPressed: _bookNewAppointment,
                tooltip: 'حجز موعد جديد',
              ),
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.textOnPrimary,
          unselectedLabelColor: AppColors.textOnPrimary.withValues(alpha: 0.7),
          indicatorColor: AppColors.textOnPrimary,
          tabs: [
            Tab(text: userRole == 'patient' ? 'القادمة' : 'اليوم'),
            Tab(text: userRole == 'patient' ? 'السابقة' : 'القادمة'),
            const Tab(text: 'الملغية'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildUpcomingAppointments(),
          _buildPastAppointments(),
          _buildCancelledAppointments(),
        ],
      ),
      floatingActionButton: userRole == 'patient'
          ? FloatingActionButton(
              onPressed: _bookNewAppointment,
              backgroundColor: AppColors.primary,
              child: const Icon(Icons.add, color: Colors.white),
            )
          : null,
    );
  }

  Widget _buildUpcomingAppointments() {
    final appointments = _getUpcomingAppointments();
    
    if (appointments.isEmpty) {
      return _buildEmptyState(
        icon: Icons.calendar_today,
        title: userRole == 'patient' ? 'لا توجد مواعيد قادمة' : 'لا توجد مواعيد اليوم',
        subtitle: userRole == 'patient' 
            ? 'احجز موعدك الأول مع طبيبك المفضل'
            : 'لا توجد مواعيد مجدولة لليوم',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
      itemCount: appointments.length,
      itemBuilder: (context, index) {
        return _buildAppointmentCard(appointments[index]);
      },
    );
  }

  Widget _buildPastAppointments() {
    final appointments = _getPastAppointments();
    
    if (appointments.isEmpty) {
      return _buildEmptyState(
        icon: Icons.history,
        title: userRole == 'patient' ? 'لا توجد مواعيد سابقة' : 'لا توجد مواعيد قادمة',
        subtitle: userRole == 'patient' 
            ? 'ستظهر هنا المواعيد التي تمت'
            : 'ستظهر هنا المواعيد القادمة',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
      itemCount: appointments.length,
      itemBuilder: (context, index) {
        return _buildAppointmentCard(appointments[index]);
      },
    );
  }

  Widget _buildCancelledAppointments() {
    final appointments = _getCancelledAppointments();
    
    if (appointments.isEmpty) {
      return _buildEmptyState(
        icon: Icons.cancel_outlined,
        title: 'لا توجد مواعيد ملغية',
        subtitle: 'ستظهر هنا المواعيد الملغية',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
      itemCount: appointments.length,
      itemBuilder: (context, index) {
        return _buildAppointmentCard(appointments[index]);
      },
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
          if (userRole == 'patient') ...[
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _bookNewAppointment,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.textOnPrimary,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('احجز موعد جديد'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAppointmentCard(Map<String, dynamic> appointment) {
    final statusGradient = _getStatusGradient(appointment['status']);

    return Card3D(
      margin: const EdgeInsets.only(bottom: 16),
      gradient: statusGradient,
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  userRole == 'patient'
                      ? appointment['doctorName'][0]
                      : appointment['patientName'][0],
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userRole == 'patient'
                          ? appointment['doctorName']
                          : appointment['patientName'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      userRole == 'patient'
                          ? appointment['specialization']
                          : appointment['reason'],
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getStatusText(appointment['status']),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Details
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.white.withValues(alpha: 0.9)),
                const SizedBox(width: 8),
                Text(
                  appointment['date'],
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                const SizedBox(width: 16),
                Icon(Icons.access_time, size: 16, color: Colors.white.withValues(alpha: 0.9)),
                const SizedBox(width: 8),
                Text(
                  appointment['time'],
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Actions
          if (appointment['status'] == 'upcoming') ...[
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _rescheduleAppointment(appointment),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.white),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'إعادة جدولة',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _cancelAppointment(appointment),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white.withValues(alpha: 0.2),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('إلغاء'),
                  ),
                ),
              ],
            ),
          ] else if (appointment['status'] == 'completed') ...[
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _viewDetails(appointment),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.white),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'عرض التفاصيل',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _bookAgain(appointment),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white.withValues(alpha: 0.2),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('احجز مرة أخرى'),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  LinearGradient _getStatusGradient(String status) {
    switch (status) {
      case 'upcoming':
        return AppColors.orangeGradient;
      case 'completed':
        return AppColors.greenGradient;
      case 'cancelled':
        return AppColors.redGradient;
      default:
        return AppColors.blueGradient;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'upcoming':
        return 'قادم';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }

  List<Map<String, dynamic>> _getUpcomingAppointments() {
    if (userRole == 'patient') {
      return [
        {
          'id': '1',
          'doctorName': 'د. سارة أحمد محمد',
          'specialization': 'أخصائي طب الأطفال والرضع',
          'date': '2024-01-15',
          'time': '10:00 صباحاً',
          'status': 'upcoming',
          'reason': 'فحص دوري شامل للطفل',
        },
        {
          'id': '2',
          'doctorName': 'د. محمد علي حسن',
          'specialization': 'أخصائي الطب الباطني',
          'date': '2024-01-18',
          'time': '2:30 مساءً',
          'status': 'upcoming',
          'reason': 'استشارة طبية ومتابعة العلاج',
        },
      ];
    } else {
      return [
        {
          'id': '1',
          'patientName': 'أحمد محمد السالم',
          'reason': 'فحص دوري شامل وتحاليل',
          'date': '2024-01-15',
          'time': '10:00 صباحاً',
          'status': 'upcoming',
        },
        {
          'id': '2',
          'patientName': 'فاطمة علي أحمد',
          'reason': 'استشارة طبية ومتابعة حالة',
          'date': '2024-01-15',
          'time': '11:00 صباحاً',
          'status': 'upcoming',
        },
      ];
    }
  }

  List<Map<String, dynamic>> _getPastAppointments() {
    if (userRole == 'patient') {
      return [
        {
          'id': '3',
          'doctorName': 'د. خالد حسن',
          'specialization': 'طب العيون',
          'date': '2024-01-10',
          'time': '9:00 ص',
          'status': 'completed',
          'reason': 'فحص النظر',
        },
      ];
    } else {
      return [
        {
          'id': '3',
          'patientName': 'سعد الأحمد',
          'reason': 'فحص النظر',
          'date': '2024-01-20',
          'time': '9:00 ص',
          'status': 'upcoming',
        },
      ];
    }
  }

  List<Map<String, dynamic>> _getCancelledAppointments() {
    return [
      {
        'id': '4',
        'doctorName': 'د. نور الدين',
        'patientName': 'مريم أحمد',
        'specialization': 'طب الأسنان',
        'reason': 'تنظيف الأسنان',
        'date': '2024-01-12',
        'time': '3:00 م',
        'status': 'cancelled',
      },
    ];
  }

  void _bookNewAppointment() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('حجز موعد جديد قريباً...'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _rescheduleAppointment(Map<String, dynamic> appointment) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('إعادة جدولة الموعد قريباً...'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _cancelAppointment(Map<String, dynamic> appointment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الموعد'),
        content: const Text('هل أنت متأكد من إلغاء هذا الموعد؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('لا'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('تم إلغاء الموعد بنجاح'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('نعم'),
          ),
        ],
      ),
    );
  }

  void _viewDetails(Map<String, dynamic> appointment) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('عرض تفاصيل الموعد قريباً...'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _bookAgain(Map<String, dynamic> appointment) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('حجز موعد جديد مع نفس الطبيب قريباً...'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
