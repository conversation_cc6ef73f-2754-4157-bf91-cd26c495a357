import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medicare_app/core/base/base_bloc.dart';
import 'package:medicare_app/core/base/base_event.dart';
import 'package:medicare_app/core/base/base_state.dart';
import 'package:medicare_app/core/errors/failures.dart';
import 'package:medicare_app/features/authentication/data/models/user_model.dart';
import 'package:medicare_app/features/authentication/data/repositories/auth_repository.dart';
import 'package:medicare_app/features/authentication/presentation/bloc/auth_event.dart';
import 'package:medicare_app/features/authentication/presentation/bloc/auth_state.dart';

class AuthBloc extends BaseBloc<AuthEvent, AuthState> {
  final AuthRepository _authRepository;
  StreamSubscription? _authStateSubscription;

  AuthBloc({
    required AuthRepository authRepository,
  })  : _authRepository = authRepository,
        super(const AuthInitial()) {
    
    // Register event handlers
    on<AuthCheckStatusEvent>(_onCheckAuthStatus);
    on<AuthLoginEvent>(_onLogin);
    on<AuthRegisterEvent>(_onRegister);
    on<AuthLogoutEvent>(_onLogout);
    on<AuthForgotPasswordEvent>(_onForgotPassword);
    on<AuthChangePasswordEvent>(_onChangePassword);
    on<AuthUpdateProfileEvent>(_onUpdateProfile);
    on<AuthDeleteAccountEvent>(_onDeleteAccount);
    on<AuthSendEmailVerificationEvent>(_onSendEmailVerification);
    on<AuthStateChangedEvent>(_onAuthStateChanged);

    // Listen to auth state changes
    _authStateSubscription = _authRepository.authStateChanges.listen((user) {
      add(AuthStateChangedEvent(user));
    });
  }

  @override
  Future<void> onLoad(LoadEvent event, Emitter<AuthState> emit) async {
    add(const AuthCheckStatusEvent());
  }

  @override
  Future<void> onRefresh(RefreshEvent event, Emitter<AuthState> emit) async {
    add(const AuthCheckStatusEvent());
  }

  @override
  Future<void> onReset(ResetEvent event, Emitter<AuthState> emit) async {
    emit(const AuthInitial());
  }

  @override
  Future<void> onRetry(RetryEvent event, Emitter<AuthState> emit) async {
    add(const AuthCheckStatusEvent());
  }

  Future<void> _onCheckAuthStatus(
    AuthCheckStatusEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      final user = await _authRepository.getCurrentUser();
      if (user != null) {
        final isEmailVerified = await _authRepository.isEmailVerified();
        if (isEmailVerified) {
          emit(AuthAuthenticated(user: user));
        } else {
          emit(AuthEmailNotVerified(user: user));
        }
      } else {
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError(failure: _mapExceptionToFailure(e)));
    }
  }

  Future<void> _onLogin(
    AuthLoginEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      final user = await _authRepository.signInWithEmailAndPassword(
        event.email,
        event.password,
      );
      
      final isEmailVerified = await _authRepository.isEmailVerified();
      if (isEmailVerified) {
        emit(AuthAuthenticated(user: user));
      } else {
        emit(AuthEmailNotVerified(user: user));
      }
    } catch (e) {
      emit(AuthError(failure: _mapExceptionToFailure(e)));
    }
  }

  Future<void> _onRegister(
    AuthRegisterEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      final user = await _authRepository.registerWithEmailAndPassword(event.userData);
      emit(AuthEmailNotVerified(user: user));
    } catch (e) {
      emit(AuthError(failure: _mapExceptionToFailure(e)));
    }
  }

  Future<void> _onLogout(
    AuthLogoutEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      await _authRepository.signOut();
      emit(const AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(failure: _mapExceptionToFailure(e)));
    }
  }

  Future<void> _onForgotPassword(
    AuthForgotPasswordEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      await _authRepository.sendPasswordResetEmail(event.email);
      emit(const AuthPasswordResetSent());
    } catch (e) {
      emit(AuthError(failure: _mapExceptionToFailure(e)));
    }
  }

  Future<void> _onChangePassword(
    AuthChangePasswordEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      await _authRepository.changePassword(
        event.currentPassword,
        event.newPassword,
      );
      
      final user = await _authRepository.getCurrentUser();
      if (user != null) {
        emit(AuthPasswordChanged(user: user));
      } else {
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError(failure: _mapExceptionToFailure(e)));
    }
  }

  Future<void> _onUpdateProfile(
    AuthUpdateProfileEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      await _authRepository.updateUserProfile(event.user);
      emit(AuthProfileUpdated(user: event.user));
    } catch (e) {
      emit(AuthError(failure: _mapExceptionToFailure(e)));
    }
  }

  Future<void> _onDeleteAccount(
    AuthDeleteAccountEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    
    try {
      await _authRepository.deleteAccount();
      emit(const AuthAccountDeleted());
    } catch (e) {
      emit(AuthError(failure: _mapExceptionToFailure(e)));
    }
  }

  Future<void> _onSendEmailVerification(
    AuthSendEmailVerificationEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      await _authRepository.sendEmailVerification();
      if (state is AuthEmailNotVerified) {
        final currentState = state as AuthEmailNotVerified;
        emit(AuthEmailVerificationSent(user: currentState.user));
      }
    } catch (e) {
      emit(AuthError(failure: _mapExceptionToFailure(e)));
    }
  }

  Future<void> _onAuthStateChanged(
    AuthStateChangedEvent event,
    Emitter<AuthState> emit,
  ) async {
    if (event.user == null) {
      emit(const AuthUnauthenticated());
    } else {
      try {
        final user = await _authRepository.getCurrentUser();
        if (user != null) {
          final isEmailVerified = await _authRepository.isEmailVerified();
          if (isEmailVerified) {
            emit(AuthAuthenticated(user: user));
          } else {
            emit(AuthEmailNotVerified(user: user));
          }
        } else {
          emit(const AuthUnauthenticated());
        }
      } catch (e) {
        emit(AuthError(failure: _mapExceptionToFailure(e)));
      }
    }
  }

  Failure _mapExceptionToFailure(dynamic exception) {
    if (exception is Failure) {
      return exception;
    }
    
    final message = exception.toString();
    
    if (message.contains('auth') || message.contains('unauthorized')) {
      return AuthenticationFailure(message: message);
    } else if (message.contains('network') || message.contains('connection')) {
      return NetworkFailure(message: message);
    } else if (message.contains('server') || message.contains('http')) {
      return ServerFailure(message: message);
    } else {
      return UnknownFailure(message: message);
    }
  }

  @override
  Future<void> close() {
    _authStateSubscription?.cancel();
    return super.close();
  }
}
