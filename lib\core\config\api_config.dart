class ApiConfig {
  // Google Gemini API Configuration
  static const String geminiApiKey = 'YOUR_GEMINI_API_KEY_HERE';
  
  // OpenAI API Configuration (if needed)
  static const String openaiApiKey = 'YOUR_OPENAI_API_KEY_HERE';
  
  // Firebase Configuration
  static const String firebaseApiKey = 'YOUR_FIREBASE_API_KEY_HERE';
  
  // Agora Configuration
  static const String agoraAppId = 'YOUR_AGORA_APP_ID_HERE';
  
  // Stripe Configuration
  static const String stripePublishableKey = 'YOUR_STRIPE_PUBLISHABLE_KEY_HERE';
  
  // API Base URLs
  static const String baseUrl = 'https://api.medicare.com';
  static const String geminiBaseUrl = 'https://generativelanguage.googleapis.com';
  
  // API Endpoints
  static const String authEndpoint = '/auth';
  static const String usersEndpoint = '/users';
  static const String appointmentsEndpoint = '/appointments';
  static const String messagesEndpoint = '/messages';
  static const String medicalRecordsEndpoint = '/medical-records';
  
  // Check if APIs are configured
  static bool get isGeminiConfigured => 
      geminiApiKey != 'YOUR_GEMINI_API_KEY_HERE' && geminiApiKey.isNotEmpty;
  
  static bool get isOpenAIConfigured => 
      openaiApiKey != 'YOUR_OPENAI_API_KEY_HERE' && openaiApiKey.isNotEmpty;
  
  static bool get isFirebaseConfigured => 
      firebaseApiKey != 'YOUR_FIREBASE_API_KEY_HERE' && firebaseApiKey.isNotEmpty;
  
  static bool get isAgoraConfigured => 
      agoraAppId != 'YOUR_AGORA_APP_ID_HERE' && agoraAppId.isNotEmpty;
  
  static bool get isStripeConfigured => 
      stripePublishableKey != 'YOUR_STRIPE_PUBLISHABLE_KEY_HERE' && stripePublishableKey.isNotEmpty;
  
  // Development/Testing Configuration
  static const bool isDevelopment = true;
  static const bool enableLogging = true;
  static const bool enableMockData = true;
  
  // Timeout configurations
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration uploadTimeout = Duration(minutes: 5);
  
  // Cache configurations
  static const Duration cacheExpiry = Duration(hours: 24);
  static const int maxCacheSize = 100; // MB
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File upload limits
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx', 'txt'];
  
  // Security
  static const int maxLoginAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 15);
  static const Duration sessionTimeout = Duration(hours: 24);
  
  // Notification settings
  static const bool enablePushNotifications = true;
  static const bool enableEmailNotifications = true;
  static const bool enableSMSNotifications = false;
  
  // Feature flags
  static const bool enableAIFeatures = true;
  static const bool enableVideoCall = true;
  static const bool enablePayments = true;
  static const bool enableMedicalRecords = true;
  static const bool enableAppointmentBooking = true;
  
  // AI Configuration
  static const double aiTemperature = 0.7;
  static const int aiMaxTokens = 1024;
  static const int aiTopK = 40;
  static const double aiTopP = 0.95;
  
  // Localization
  static const String defaultLanguage = 'ar';
  static const List<String> supportedLanguages = ['ar', 'en'];
  
  // Theme
  static const String defaultTheme = 'light';
  static const List<String> availableThemes = ['light', 'dark', 'system'];
  
  // Error messages
  static const Map<String, String> errorMessages = {
    'network_error': 'خطأ في الاتصال بالشبكة',
    'server_error': 'خطأ في الخادم',
    'unauthorized': 'غير مصرح لك بالوصول',
    'not_found': 'المورد غير موجود',
    'validation_error': 'خطأ في التحقق من البيانات',
    'timeout_error': 'انتهت مهلة الاتصال',
    'unknown_error': 'حدث خطأ غير معروف',
  };
  
  // Success messages
  static const Map<String, String> successMessages = {
    'login_success': 'تم تسجيل الدخول بنجاح',
    'register_success': 'تم التسجيل بنجاح',
    'update_success': 'تم التحديث بنجاح',
    'delete_success': 'تم الحذف بنجاح',
    'save_success': 'تم الحفظ بنجاح',
    'send_success': 'تم الإرسال بنجاح',
  };
  
  // API Keys validation
  static String? validateApiKey(String key, String service) {
    if (key.isEmpty) {
      return 'مفتاح $service مطلوب';
    }
    
    if (key.startsWith('YOUR_') && key.endsWith('_HERE')) {
      return 'يرجى تكوين مفتاح $service الصحيح';
    }
    
    // Basic format validation
    switch (service.toLowerCase()) {
      case 'gemini':
        if (!key.startsWith('AI') || key.length < 20) {
          return 'تنسيق مفتاح Gemini غير صحيح';
        }
        break;
      case 'openai':
        if (!key.startsWith('sk-') || key.length < 40) {
          return 'تنسيق مفتاح OpenAI غير صحيح';
        }
        break;
      case 'stripe':
        if (!key.startsWith('pk_') || key.length < 20) {
          return 'تنسيق مفتاح Stripe غير صحيح';
        }
        break;
    }
    
    return null; // Valid
  }
  
  // Get configuration summary
  static Map<String, bool> getConfigurationStatus() {
    return {
      'Gemini AI': isGeminiConfigured,
      'OpenAI': isOpenAIConfigured,
      'Firebase': isFirebaseConfigured,
      'Agora Video': isAgoraConfigured,
      'Stripe Payments': isStripeConfigured,
    };
  }
  
  // Environment-specific configurations
  static Map<String, dynamic> getEnvironmentConfig() {
    if (isDevelopment) {
      return {
        'baseUrl': 'https://dev-api.medicare.com',
        'enableLogging': true,
        'enableMockData': true,
        'apiTimeout': const Duration(seconds: 60),
      };
    } else {
      return {
        'baseUrl': 'https://api.medicare.com',
        'enableLogging': false,
        'enableMockData': false,
        'apiTimeout': const Duration(seconds: 30),
      };
    }
  }
}
